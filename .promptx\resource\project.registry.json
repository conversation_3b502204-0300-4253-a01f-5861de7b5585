{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-30T09:17:27.630Z", "updatedAt": "2025-07-30T09:17:27.684Z", "resourceCount": 24}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-07-30T09:17:27.634Z", "updatedAt": "2025-07-30T09:17:27.634Z", "scannedAt": "2025-07-30T09:17:27.634Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "ai-integration-process", "source": "project", "protocol": "execution", "name": "Ai Integration Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-integration-expert/execution/ai-integration-process.execution.md", "metadata": {"createdAt": "2025-07-30T09:17:27.639Z", "updatedAt": "2025-07-30T09:17:27.639Z", "scannedAt": "2025-07-30T09:17:27.639Z", "path": "role/ai-integration-expert/execution/ai-integration-process.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:17:27.641Z", "updatedAt": "2025-07-30T09:17:27.641Z", "scannedAt": "2025-07-30T09:17:27.641Z", "path": "role/ai-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "ai-novel-architect", "source": "project", "protocol": "role", "name": "Ai Novel Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-architect/ai-novel-architect.role.md", "metadata": {"createdAt": "2025-07-30T09:17:27.644Z", "updatedAt": "2025-07-30T09:17:27.644Z", "scannedAt": "2025-07-30T09:17:27.644Z", "path": "role/ai-novel-architect/ai-novel-architect.role.md"}}, {"id": "ai-novel-development", "source": "project", "protocol": "execution", "name": "Ai Novel Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-architect/execution/ai-novel-development.execution.md", "metadata": {"createdAt": "2025-07-30T09:17:27.646Z", "updatedAt": "2025-07-30T09:17:27.646Z", "scannedAt": "2025-07-30T09:17:27.646Z", "path": "role/ai-novel-architect/execution/ai-novel-development.execution.md"}}, {"id": "ai-novel-thinking", "source": "project", "protocol": "thought", "name": "Ai Novel Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-architect/thought/ai-novel-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:17:27.648Z", "updatedAt": "2025-07-30T09:17:27.648Z", "scannedAt": "2025-07-30T09:17:27.648Z", "path": "role/ai-novel-architect/thought/ai-novel-thinking.thought.md"}}, {"id": "ai-novel-writer", "source": "project", "protocol": "role", "name": "Ai Novel Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-writer/ai-novel-writer.role.md", "metadata": {"createdAt": "2025-07-30T09:17:27.650Z", "updatedAt": "2025-07-30T09:17:27.650Z", "scannedAt": "2025-07-30T09:17:27.650Z", "path": "role/ai-novel-writer/ai-novel-writer.role.md"}}, {"id": "creative-writing-process", "source": "project", "protocol": "execution", "name": "Creative Writing Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-writer/execution/creative-writing-process.execution.md", "metadata": {"createdAt": "2025-07-30T09:17:27.651Z", "updatedAt": "2025-07-30T09:17:27.651Z", "scannedAt": "2025-07-30T09:17:27.651Z", "path": "role/ai-novel-writer/execution/creative-writing-process.execution.md"}}, {"id": "creative-writing-thinking", "source": "project", "protocol": "thought", "name": "Creative Writing Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-writer/thought/creative-writing-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:17:27.654Z", "updatedAt": "2025-07-30T09:17:27.654Z", "scannedAt": "2025-07-30T09:17:27.654Z", "path": "role/ai-novel-writer/thought/creative-writing-thinking.thought.md"}}, {"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-07-30T09:17:27.657Z", "updatedAt": "2025-07-30T09:17:27.657Z", "scannedAt": "2025-07-30T09:17:27.657Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-07-30T09:17:27.659Z", "updatedAt": "2025-07-30T09:17:27.659Z", "scannedAt": "2025-07-30T09:17:27.659Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:17:27.662Z", "updatedAt": "2025-07-30T09:17:27.662Z", "scannedAt": "2025-07-30T09:17:27.662Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}, {"id": "glassmorphism-design-process", "source": "project", "protocol": "execution", "name": "Glassmorphism Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md", "metadata": {"createdAt": "2025-07-30T09:17:27.665Z", "updatedAt": "2025-07-30T09:17:27.665Z", "scannedAt": "2025-07-30T09:17:27.665Z", "path": "role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md"}}, {"id": "glassmorphism-designer", "source": "project", "protocol": "role", "name": "Glassmorphism Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/glassmorphism-designer/glassmorphism-designer.role.md", "metadata": {"createdAt": "2025-07-30T09:17:27.666Z", "updatedAt": "2025-07-30T09:17:27.666Z", "scannedAt": "2025-07-30T09:17:27.666Z", "path": "role/glassmorphism-designer/glassmorphism-designer.role.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:17:27.668Z", "updatedAt": "2025-07-30T09:17:27.668Z", "scannedAt": "2025-07-30T09:17:27.668Z", "path": "role/glassmorphism-designer/thought/design-thinking.thought.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "execution", "name": "Intelligent Routing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/intelligent-routing.execution.md", "metadata": {"createdAt": "2025-07-30T09:17:27.672Z", "updatedAt": "2025-07-30T09:17:27.672Z", "scannedAt": "2025-07-30T09:17:27.672Z", "path": "role/system-director/execution/intelligent-routing.execution.md"}}, {"id": "project-management", "source": "project", "protocol": "execution", "name": "Project Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-management.execution.md", "metadata": {"createdAt": "2025-07-30T09:17:27.673Z", "updatedAt": "2025-07-30T09:17:27.673Z", "scannedAt": "2025-07-30T09:17:27.673Z", "path": "role/system-director/execution/project-management.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-07-30T09:17:27.675Z", "updatedAt": "2025-07-30T09:17:27.675Z", "scannedAt": "2025-07-30T09:17:27.675Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "team-coordination", "source": "project", "protocol": "execution", "name": "Team Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/team-coordination.execution.md", "metadata": {"createdAt": "2025-07-30T09:17:27.676Z", "updatedAt": "2025-07-30T09:17:27.676Z", "scannedAt": "2025-07-30T09:17:27.676Z", "path": "role/system-director/execution/team-coordination.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-07-30T09:17:27.677Z", "updatedAt": "2025-07-30T09:17:27.677Z", "scannedAt": "2025-07-30T09:17:27.677Z", "path": "role/system-director/system-director.role.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "thought", "name": "Intelligent Routing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/intelligent-routing.thought.md", "metadata": {"createdAt": "2025-07-30T09:17:27.679Z", "updatedAt": "2025-07-30T09:17:27.679Z", "scannedAt": "2025-07-30T09:17:27.679Z", "path": "role/system-director/thought/intelligent-routing.thought.md"}}, {"id": "quality-control", "source": "project", "protocol": "thought", "name": "Quality Control 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/quality-control.thought.md", "metadata": {"createdAt": "2025-07-30T09:17:27.680Z", "updatedAt": "2025-07-30T09:17:27.680Z", "scannedAt": "2025-07-30T09:17:27.680Z", "path": "role/system-director/thought/quality-control.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:17:27.681Z", "updatedAt": "2025-07-30T09:17:27.681Z", "scannedAt": "2025-07-30T09:17:27.681Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "team-coordination", "source": "project", "protocol": "thought", "name": "Team Coordination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/team-coordination.thought.md", "metadata": {"createdAt": "2025-07-30T09:17:27.682Z", "updatedAt": "2025-07-30T09:17:27.682Z", "scannedAt": "2025-07-30T09:17:27.682Z", "path": "role/system-director/thought/team-coordination.thought.md"}}], "stats": {"totalResources": 24, "byProtocol": {"role": 6, "execution": 9, "thought": 9}, "bySource": {"project": 24}}}