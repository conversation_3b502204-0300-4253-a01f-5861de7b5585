{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-30T01:06:23.915Z", "updatedAt": "2025-07-30T01:06:23.981Z", "resourceCount": 24}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-07-30T01:06:23.920Z", "updatedAt": "2025-07-30T01:06:23.920Z", "scannedAt": "2025-07-30T01:06:23.920Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "ai-integration-process", "source": "project", "protocol": "execution", "name": "Ai Integration Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-integration-expert/execution/ai-integration-process.execution.md", "metadata": {"createdAt": "2025-07-30T01:06:23.923Z", "updatedAt": "2025-07-30T01:06:23.923Z", "scannedAt": "2025-07-30T01:06:23.923Z", "path": "role/ai-integration-expert/execution/ai-integration-process.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T01:06:23.925Z", "updatedAt": "2025-07-30T01:06:23.925Z", "scannedAt": "2025-07-30T01:06:23.925Z", "path": "role/ai-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "ai-novel-architect", "source": "project", "protocol": "role", "name": "Ai Novel Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-architect/ai-novel-architect.role.md", "metadata": {"createdAt": "2025-07-30T01:06:23.930Z", "updatedAt": "2025-07-30T01:06:23.930Z", "scannedAt": "2025-07-30T01:06:23.930Z", "path": "role/ai-novel-architect/ai-novel-architect.role.md"}}, {"id": "ai-novel-development", "source": "project", "protocol": "execution", "name": "Ai Novel Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-architect/execution/ai-novel-development.execution.md", "metadata": {"createdAt": "2025-07-30T01:06:23.932Z", "updatedAt": "2025-07-30T01:06:23.932Z", "scannedAt": "2025-07-30T01:06:23.932Z", "path": "role/ai-novel-architect/execution/ai-novel-development.execution.md"}}, {"id": "ai-novel-thinking", "source": "project", "protocol": "thought", "name": "Ai Novel Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-architect/thought/ai-novel-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T01:06:23.935Z", "updatedAt": "2025-07-30T01:06:23.935Z", "scannedAt": "2025-07-30T01:06:23.935Z", "path": "role/ai-novel-architect/thought/ai-novel-thinking.thought.md"}}, {"id": "ai-novel-writer", "source": "project", "protocol": "role", "name": "Ai Novel Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-writer/ai-novel-writer.role.md", "metadata": {"createdAt": "2025-07-30T01:06:23.938Z", "updatedAt": "2025-07-30T01:06:23.938Z", "scannedAt": "2025-07-30T01:06:23.938Z", "path": "role/ai-novel-writer/ai-novel-writer.role.md"}}, {"id": "creative-writing-process", "source": "project", "protocol": "execution", "name": "Creative Writing Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-writer/execution/creative-writing-process.execution.md", "metadata": {"createdAt": "2025-07-30T01:06:23.941Z", "updatedAt": "2025-07-30T01:06:23.941Z", "scannedAt": "2025-07-30T01:06:23.941Z", "path": "role/ai-novel-writer/execution/creative-writing-process.execution.md"}}, {"id": "creative-writing-thinking", "source": "project", "protocol": "thought", "name": "Creative Writing Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-writer/thought/creative-writing-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T01:06:23.945Z", "updatedAt": "2025-07-30T01:06:23.945Z", "scannedAt": "2025-07-30T01:06:23.945Z", "path": "role/ai-novel-writer/thought/creative-writing-thinking.thought.md"}}, {"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-07-30T01:06:23.948Z", "updatedAt": "2025-07-30T01:06:23.948Z", "scannedAt": "2025-07-30T01:06:23.948Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-07-30T01:06:23.950Z", "updatedAt": "2025-07-30T01:06:23.950Z", "scannedAt": "2025-07-30T01:06:23.950Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T01:06:23.952Z", "updatedAt": "2025-07-30T01:06:23.952Z", "scannedAt": "2025-07-30T01:06:23.952Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}, {"id": "glassmorphism-design-process", "source": "project", "protocol": "execution", "name": "Glassmorphism Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md", "metadata": {"createdAt": "2025-07-30T01:06:23.956Z", "updatedAt": "2025-07-30T01:06:23.956Z", "scannedAt": "2025-07-30T01:06:23.956Z", "path": "role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md"}}, {"id": "glassmorphism-designer", "source": "project", "protocol": "role", "name": "Glassmorphism Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/glassmorphism-designer/glassmorphism-designer.role.md", "metadata": {"createdAt": "2025-07-30T01:06:23.957Z", "updatedAt": "2025-07-30T01:06:23.957Z", "scannedAt": "2025-07-30T01:06:23.957Z", "path": "role/glassmorphism-designer/glassmorphism-designer.role.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T01:06:23.959Z", "updatedAt": "2025-07-30T01:06:23.959Z", "scannedAt": "2025-07-30T01:06:23.959Z", "path": "role/glassmorphism-designer/thought/design-thinking.thought.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "execution", "name": "Intelligent Routing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/intelligent-routing.execution.md", "metadata": {"createdAt": "2025-07-30T01:06:23.964Z", "updatedAt": "2025-07-30T01:06:23.964Z", "scannedAt": "2025-07-30T01:06:23.964Z", "path": "role/system-director/execution/intelligent-routing.execution.md"}}, {"id": "project-management", "source": "project", "protocol": "execution", "name": "Project Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-management.execution.md", "metadata": {"createdAt": "2025-07-30T01:06:23.966Z", "updatedAt": "2025-07-30T01:06:23.966Z", "scannedAt": "2025-07-30T01:06:23.966Z", "path": "role/system-director/execution/project-management.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-07-30T01:06:23.968Z", "updatedAt": "2025-07-30T01:06:23.968Z", "scannedAt": "2025-07-30T01:06:23.968Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "team-coordination", "source": "project", "protocol": "execution", "name": "Team Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/team-coordination.execution.md", "metadata": {"createdAt": "2025-07-30T01:06:23.969Z", "updatedAt": "2025-07-30T01:06:23.969Z", "scannedAt": "2025-07-30T01:06:23.969Z", "path": "role/system-director/execution/team-coordination.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-07-30T01:06:23.971Z", "updatedAt": "2025-07-30T01:06:23.971Z", "scannedAt": "2025-07-30T01:06:23.971Z", "path": "role/system-director/system-director.role.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "thought", "name": "Intelligent Routing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/intelligent-routing.thought.md", "metadata": {"createdAt": "2025-07-30T01:06:23.974Z", "updatedAt": "2025-07-30T01:06:23.974Z", "scannedAt": "2025-07-30T01:06:23.974Z", "path": "role/system-director/thought/intelligent-routing.thought.md"}}, {"id": "quality-control", "source": "project", "protocol": "thought", "name": "Quality Control 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/quality-control.thought.md", "metadata": {"createdAt": "2025-07-30T01:06:23.975Z", "updatedAt": "2025-07-30T01:06:23.975Z", "scannedAt": "2025-07-30T01:06:23.975Z", "path": "role/system-director/thought/quality-control.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T01:06:23.977Z", "updatedAt": "2025-07-30T01:06:23.977Z", "scannedAt": "2025-07-30T01:06:23.977Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "team-coordination", "source": "project", "protocol": "thought", "name": "Team Coordination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/team-coordination.thought.md", "metadata": {"createdAt": "2025-07-30T01:06:23.979Z", "updatedAt": "2025-07-30T01:06:23.979Z", "scannedAt": "2025-07-30T01:06:23.979Z", "path": "role/system-director/thought/team-coordination.thought.md"}}], "stats": {"totalResources": 24, "byProtocol": {"role": 6, "execution": 9, "thought": 9}, "bySource": {"project": 24}}}