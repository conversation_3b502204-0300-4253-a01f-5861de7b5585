# AI小说助手开发文档

## 文档版本信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-29
- **最后更新**: 2025-07-30
- **文档状态**: 已完成

---

## 1. 项目概述

### 1.1 项目简介
AI小说助手是一款基于Electron + Vue 3技术栈开发的桌面应用程序，专为网络小说创作者设计。应用集成多种AI模型（GPT、Claude、Gemini、ModelScope、Ollama、SiliconFlow等），提供从大纲生成到章节创作的全流程智能辅助功能。

### 1.2 项目目标
- **零配置启动**: 内置所有依赖，用户下载即可使用
- **多AI模型支持**: 统一管理多个AI服务商的API
- **智能创作辅助**: 提供大纲生成、章节编写、人物管理等核心功能
- **现代化界面**: 采用Glassmorphism设计风格，支持明暗主题切换
- **跨平台兼容**: 支持Windows、macOS、Linux三大平台

### 1.3 目标用户
- 网络小说创作者（番茄小说、飞卢、17K、起点、纵横、晋江、七猫等平台作者）
- 业余写作爱好者
- 需要AI辅助创作的内容创作者

---

## 2. 技术架构

### 2.1 技术栈选择

#### 2.1.1 前端技术栈
```
Electron 28.x          # 桌面应用框架
├── Vue 3.4.x          # 前端框架
├── TypeScript 5.x     # 类型安全
├── Vite 5.x           # 构建工具
├── Element Plus 2.x   # UI组件库
├── Pinia 2.x          # 状态管理
├── Vue Router 4.x     # 路由管理
└── Tailwind CSS 3.x   # 样式框架
```

#### 2.1.2 后端技术栈
```
Node.js 20.x           # 运行环境（Electron内置）
├── SQLite 3.x         # 本地数据库（内置）
├── Prisma 5.x         # ORM框架（内置）
├── Fetch API          # HTTP客户端（Electron内置）
└── Web Crypto API     # 加密库（浏览器内置）
```

#### 2.1.3 内置依赖策略
为确保用户零配置启动，应用采用以下内置依赖策略：

**核心原则**：
- 所有运行时依赖内置到应用包中
- 避免用户手动安装Node.js、Python等环境
- 不依赖外部系统服务或数据库
- 使用轻量级、纯JavaScript实现

**内置组件清单**：
```javascript
const builtInDependencies = {
  // 数据存储
  database: "SQLite (内置，无需安装)",

  // 文本处理
  textProcessing: "纯JavaScript实现",

  // 文件操作
  fileSystem: "Electron原生API",

  // 网络请求
  httpClient: "Electron内置fetch API",

  // 加密解密
  encryption: "Web Crypto API",

  // 向量计算
  vectorComputation: "TensorFlow.js (轻量版)",

  // 界面渲染
  uiFramework: "Vue 3 + Element Plus (打包内置)",

  // 状态管理
  stateManagement: "Pinia (内置)",

  // 路由管理
  routing: "Vue Router (内置)"
};
```

**避免的技术**：
- ❌ 外部数据库（MySQL、PostgreSQL等）
- ❌ 后端服务框架（Express、Koa等）
- ❌ 复杂的机器学习框架
- ❌ 需要编译的原生模块
- ❌ 企业级中间件和服务

#### 2.1.4 AI集成技术
```
AI服务集成
├── OpenAI API        # GPT系列模型
├── Anthropic API     # Claude系列模型
├── Google AI API     # Gemini系列模型
├── ModelScope API    # 国产模型平台
├── SiliconFlow API   # 高性能AI服务
├── Ollama API        # 本地模型服务
└── 自定义OpenAI兼容API
```

### 2.2 系统架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    AI小说助手桌面应用                        │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Electron Renderer Process)                        │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ 大纲生成    │ 章节编辑    │ 人物管理    │ 统计分析    │  │
│  ├─────────────┼─────────────┼─────────────┼─────────────┤  │
│  │ Vue 3 + TypeScript + Element Plus + Tailwind CSS    │  │
│  └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  主进程层 (Electron Main Process)                          │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ 窗口管理    │ 文件系统    │ 系统集成    │ 安全控制    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Electron Main Process)                        │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ AI服务管理  │ 数据处理    │ 上下文管理  │ 模板引擎    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  数据存储层 (SQLite + File System)                         │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ 项目数据    │ 用户配置    │ 模板库      │ 缓存数据    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  AI服务层 (External APIs)                                  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ OpenAI      │ Anthropic   │ Google AI   │ 本地模型    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 AI服务集成架构

AI服务集成是应用的核心技术架构，需要支持多种AI服务商的统一接入和管理。

#### 2.3.1 智能API地址检测机制

**核心设计原则**：
- 不强制给API地址加上后缀，而是智能检测分析用户地址的有效性
- 只有在API地址无效导致无法成功连接时，才进行纠正匹配后缀
- 对于有效的能够成功连接的API地址，无需纠正匹配后缀

**检测流程设计**：
```mermaid
flowchart TD
    A[用户输入API地址] --> B[格式验证]
    B --> C{格式正确?}
    C -->|否| D[提示格式错误]
    C -->|是| E[尝试连接测试]
    E --> F{连接成功?}
    F -->|是| G[保存原始地址]
    F -->|否| H[智能地址纠正]
    H --> I[尝试纠正后地址]
    I --> J{纠正后连接成功?}
    J -->|是| K[保存纠正后地址并提示]
    J -->|否| L[连接失败，提示用户检查]
```

**智能纠正规则**：
```typescript
interface APICorrection {
  provider: string;
  patterns: {
    detect: RegExp;
    correct: string;
    description: string;
  }[];
}

const apiCorrectionRules: APICorrection[] = [
  {
    provider: "OpenAI",
    patterns: [
      {
        detect: /^https?:\/\/api\.openai\.com\/?$/,
        correct: "https://api.openai.com/v1/chat/completions",
        description: "自动添加完整的API端点路径"
      },
      {
        detect: /^https?:\/\/api\.openai\.com\/v1\/?$/,
        correct: "https://api.openai.com/v1/chat/completions",
        description: "自动添加chat/completions端点"
      }
    ]
  },
  {
    provider: "Anthropic",
    patterns: [
      {
        detect: /^https?:\/\/api\.anthropic\.com\/?$/,
        correct: "https://api.anthropic.com/v1/messages",
        description: "自动添加完整的API端点路径"
      }
    ]
  },
  {
    provider: "Custom",
    patterns: [
      {
        detect: /\/chat\/completions\/?$/,
        correct: "$&", // 保持原样
        description: "标准OpenAI兼容端点，无需修改"
      },
      {
        detect: /\/v1\/?$/,
        correct: "$&/chat/completions",
        description: "自动添加chat/completions端点"
      }
    ]
  }
];
```

#### 2.3.2 统一API保存管理系统

**设计目标**：
- 方便用户快速查看查找已保存配置好的API
- 用户只需填写正确的API密钥、API地址和AI模型配置
- 测试连接成功后保存配置，即可全局使用
- 支持多配置管理和快速切换

**管理架构**：
```typescript
interface UnifiedAPIManager {
  // 配置存储
  configs: Map<string, APIConfig>;

  // 当前活动配置
  activeConfigs: Map<string, string>; // provider -> configId

  // 配置操作
  addConfig(config: APIConfig): Promise<string>;
  updateConfig(id: string, config: Partial<APIConfig>): Promise<void>;
  deleteConfig(id: string): Promise<void>;

  // 连接测试
  testConnection(id: string): Promise<TestResult>;
  batchTestAll(): Promise<TestResult[]>;

  // 配置切换
  setActiveConfig(provider: string, configId: string): Promise<void>;
  getActiveConfig(provider: string): APIConfig | null;

  // 配置管理
  exportConfigs(): Promise<string>;
  importConfigs(data: string): Promise<void>;

  // 快速访问
  getAvailableProviders(): string[];
  getConfigsByProvider(provider: string): APIConfig[];
  searchConfigs(keyword: string): APIConfig[];
}

interface APIConfig {
  id: string;
  name: string;
  provider: string;
  apiKey: string;
  apiUrl: string;
  model: string;
  status: 'untested' | 'connected' | 'failed' | 'error';
  lastTested?: Date;
  isDefault: boolean;
  tags: string[];
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

---

### 2.4 目录结构设计

```
ai-novel-assistant/
├── src/                          # 源代码目录
│   ├── main/                     # Electron主进程
│   │   ├── index.ts              # 主进程入口
│   │   ├── window.ts             # 窗口管理
│   │   ├── menu.ts               # 菜单配置
│   │   └── ipc/                  # IPC通信
│   ├── renderer/                 # 渲染进程(前端)
│   │   ├── src/                  # Vue应用源码
│   │   │   ├── components/       # 通用组件
│   │   │   ├── views/            # 页面组件
│   │   │   ├── stores/           # Pinia状态管理
│   │   │   ├── router/           # 路由配置
│   │   │   ├── utils/            # 工具函数
│   │   │   ├── types/            # TypeScript类型
│   │   │   ├── styles/           # 样式文件
│   │   │   └── assets/           # 静态资源
│   │   ├── index.html            # HTML模板
│   │   └── vite.config.ts        # Vite配置
│   └── backend/                  # 后端服务
│       ├── server.ts             # 服务器入口
│       ├── routes/               # API路由
│       ├── services/             # 业务服务
│       ├── models/               # 数据模型
│       ├── utils/                # 工具函数
│       └── database/             # 数据库相关
├── resources/                    # 资源文件
│   ├── icons/                    # 应用图标
│   ├── templates/                # 提示词模板
│   └── database/                 # 数据库文件
├── dist/                         # 构建输出
├── build/                        # 构建配置
├── docs/                         # 文档目录
├── tests/                        # 测试文件
├── package.json                  # 项目配置
├── electron-builder.json         # 打包配置
├── tsconfig.json                 # TypeScript配置
├── tailwind.config.js            # Tailwind配置
└── README.md                     # 项目说明
```

---

## 3. 数据库设计

### 3.1 数据库表结构

#### 3.1.1 项目表 (projects)
```sql
CREATE TABLE projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,           -- 小说标题
    genre VARCHAR(100),                    -- 小说类型
    theme VARCHAR(100),                    -- 小说主题
    style VARCHAR(100),                    -- 小说风格
    total_chapters INTEGER DEFAULT 1,      -- 总章节数
    words_per_chapter INTEGER DEFAULT 3000, -- 每章字数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.1.2 大纲表 (outlines)
```sql
CREATE TABLE outlines (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    title VARCHAR(255),                    -- 小说标题
    core_theme TEXT,                       -- 核心主题
    story_summary TEXT,                    -- 故事梗概
    world_setting TEXT,                    -- 世界观设定
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id)
);
```

#### 3.1.3 章节表 (chapters)
```sql
CREATE TABLE chapters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    chapter_number INTEGER NOT NULL,       -- 章节号
    title VARCHAR(255),                    -- 章节标题
    summary TEXT,                          -- 章节摘要
    content TEXT,                          -- 章节内容
    word_count INTEGER DEFAULT 0,          -- 字数统计
    status VARCHAR(20) DEFAULT 'draft',    -- 状态: draft, completed
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id)
);
```

#### 3.1.4 角色表 (characters)
```sql
CREATE TABLE characters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,            -- 角色姓名
    role_type VARCHAR(50),                 -- 角色类型: 主角/重要角色/配角/反派/龙套
    identity VARCHAR(100),                 -- 身份
    personality TEXT,                      -- 性格特点
    background TEXT,                       -- 背景故事
    appearance TEXT,                       -- 外貌描述
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id)
);
```

#### 3.1.5 角色关系表 (character_relationships)
```sql
CREATE TABLE character_relationships (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    character1_id INTEGER NOT NULL,        -- 角色1
    character2_id INTEGER NOT NULL,        -- 角色2
    relationship VARCHAR(100),             -- 关系类型
    description TEXT,                      -- 关系描述
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (character1_id) REFERENCES characters(id),
    FOREIGN KEY (character2_id) REFERENCES characters(id)
);
```

#### 3.1.6 提示词模板表 (prompt_templates)
```sql
CREATE TABLE prompt_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,            -- 模板名称
    description TEXT,                      -- 模板描述
    category VARCHAR(100),                 -- 模板分类
    content TEXT NOT NULL,                 -- 模板内容
    is_builtin BOOLEAN DEFAULT FALSE,      -- 是否内置模板
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.1.7 AI配置表 (ai_configs)
```sql
CREATE TABLE ai_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    provider VARCHAR(50) NOT NULL,         -- 服务商: openai, anthropic, google等
    api_key TEXT,                          -- API密钥(加密存储)
    model_name VARCHAR(100),               -- 模型名称
    api_url VARCHAR(255),                  -- API地址
    is_active BOOLEAN DEFAULT FALSE,       -- 是否激活
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2 数据库关系图

```
projects (1) ──────── (n) outlines
    │
    ├── (1) ──────── (n) chapters
    │
    └── (1) ──────── (n) characters
                          │
                          └── (n) ──── (n) character_relationships
```

---

## 4. 核心功能模块设计

### 4.1 功能模块概览

| 序号 | 功能模块 | 核心能力 | 优先级 |
|------|----------|----------|--------|
| 1 | 大纲生成 | AI驱动的智能大纲创建 | 高 |
| 2 | 大纲编辑 | 大纲内容的编辑和优化 | 高 |
| 3 | 章节编辑 | 章节结构管理和编辑 | 高 |
| 4 | 章节生成 | AI辅助章节内容创作 | 高 |
| 5 | 人物编辑 | 角色档案管理 | 中 |
| 6 | 人物关系图 | 角色关系可视化 | 中 |
| 7 | 章节分析 | 内容质量分析和改进建议 | 中 |
| 8 | 统计信息 | 创作进度和数据统计 | 中 |
| 9 | AI聊天 | AI模型验证和写作咨询 | 中 |
| 10 | 提示词模板系统 | 模板管理和自定义 | 低 |
| 11 | 降AI味功能 | AI生成内容优化 | 中 |
| 12 | 记忆窗口功能 | 窗口状态记忆功能 | 低 |
| 13 | 设置 | 系统配置和API管理 | 高 |
| 14 | 运行日志功能 | 应用运行状态记录 | 低 |
| 15 | 向量库检索功能 | 基于语义的内容检索 | 低 |
| 16 | 上下文管理功能 | 智能上下文提取和应用 | 中 |
| 17 | 网络小说平台适配 | 针对不同平台的内容优化 | 中 |

### 4.2 模块间依赖关系

```mermaid
graph TD
    A[设置模块] --> B[AI聊天]
    A --> C[大纲生成]
    A --> D[章节生成]
    A --> E[人物编辑]
    A --> F[章节分析]
    A --> O[网络小说平台适配]

    G[提示词模板系统] --> C
    G --> D
    G --> E
    G --> F

    C --> H[大纲编辑]
    H --> I[章节编辑]
    I --> D

    E --> J[人物关系图]
    D --> F

    K[上下文管理] --> D
    K --> F

    L[向量库检索] --> K

    M[降AI味功能] --> D
    M --> F

    N[统计信息] --> C
    N --> I
    N --> D

    P[记忆窗口功能] --> A
    Q[运行日志功能] --> A

    O --> D
    O --> F
```

---

## 5. 详细功能模块设计

### 5.1 大纲生成模块

#### 5.1.1 功能概述
大纲生成模块是AI小说助手的核心功能，通过AI模型根据用户输入的基本信息生成完整的小说大纲。

#### 5.1.2 核心功能
- **模型选择**: 支持GPT、Claude、Gemini、ModelScope、Ollama、SiliconFlow等多种AI模型
- **提示词模板**: 内置标准大纲提示词模板，支持用户自定义模板
- **基本信息配置**: 小说标题、类型、主题、风格、章节数、字数等参数设置
- **生成范围控制**: 支持指定章节范围生成，便于分段创作
- **结果管理**: 生成大纲的保存、清空、重新生成等操作

#### 5.1.3 参数配置限制

**章节数配置**
- 默认值：1章
- 最小值：1章
- 最大值：9999章
- 输入验证：只允许正整数输入

**每章字数配置**
- 默认值：3000字/章
- 最小值：200字/章
- 最大值：9999字/章
- 输入验证：只允许正整数输入

**生成范围配置**
- 起始章：默认为1，最小为1，最大为设定的总章节数
- 结束章：默认为总章节数，最小为起始章，最大为设定的总章节数
- 范围验证：确保起始章 ≤ 结束章

#### 5.1.3 内置小说配置选项

**小说类型配置**
```javascript
const novelGenres = {
  xuanhuan: "玄幻",
  xiuzhen: "修真",
  xianxia: "仙侠",
  dushi: "都市",
  lishi: "历史",
  junshi: "军事",
  kehuan: "科幻",
  lingyi: "灵异",
  youxi: "游戏",
  jingji: "竞技",
  yanqing: "言情",
  gufeng: "古风",
  chuanyue: "穿越",
  chongsheng: "重生",
  xitong: "系统",
  wuxia: "武侠",
  mohuan: "魔幻",
  qihuan: "奇幻",
  moshi: "末世",
  jijia: "机甲"
};
```

**小说主题配置**
```javascript
const novelThemes = {
  // 玄幻修真类
  xiuxian: "修仙成神",
  duobao: "夺宝争霸",
  zongmen: "宗门争斗",
  tianfu: "天赋觉醒",

  // 都市类
  haomen: "豪门恩怨",
  shangzhan: "商战风云",
  xiaoyuan: "校园青春",
  zhichang: "职场奋斗",
  yisheng: "医者仁心",

  // 历史军事类
  zhengba: "乱世争霸",
  kangzhan: "抗战救国",
  diezhan: "谍战风云",
  gongting: "宫廷权谋",

  // 科幻类
  xingji: "星际征途",
  jijia: "机甲战士",
  bianyi: "末世求生",
  shikong: "时空穿梭",

  // 言情类
  zongcai: "霸道总裁",
  xiaoyuan: "校园恋情",
  gudai: "古代言情",
  xiandai: "现代都市",
  chuanyue: "穿越重生"
};
```

**小说风格配置**
```javascript
const novelStyles = {
  // 节奏风格
  shuangwen: "爽文快节奏",
  manre: "慢热细腻",
  rexue: "热血激昂",
  qingsong: "轻松幽默",

  // 情感风格
  shenshen: "深沉内敛",
  jiqing: "激情澎湃",
  wenxin: "温馨治愈",
  nuexin: "虐心催泪",

  // 叙述风格
  diyirencheng: "第一人称",
  disanrencheng: "第三人称",
  quanzhishijiao: "全知视角",

  // 文笔风格
  jianjie: "简洁明快",
  huali: "华丽辞藻",
  pushi: "朴实自然",
  shiyi: "诗意唯美"
};
```

**网络小说平台适配配置**
```javascript
const platformAdaptation = {
  qidian: {
    name: "起点中文网",
    chapterLength: { min: 3000, max: 5000, recommended: 3500 },
    updateFrequency: "日更",
    features: ["爽点设置", "节奏控制", "悬念设计"],
    tags: ["玄幻", "都市", "历史", "科幻", "游戏"],
    requirements: "注重爽点和节奏，章节标题要吸引眼球"
  },
  fanqie: {
    name: "番茄小说",
    chapterLength: { min: 1500, max: 3000, recommended: 2000 },
    updateFrequency: "日更或多更",
    features: ["快节奏", "短章节", "悬念结尾"],
    tags: ["都市", "言情", "悬疑", "历史"],
    requirements: "情节推进要快，每章结尾要有悬念"
  },
  jinjiang: {
    name: "晋江文学城",
    chapterLength: { min: 2000, max: 4000, recommended: 3000 },
    updateFrequency: "稳定更新",
    features: ["情感描写", "人物塑造", "文笔要求"],
    tags: ["言情", "耽美", "百合", "古风"],
    requirements: "注重情感细腻描写，角色要立体丰满"
  },
  qimao: {
    name: "七猫小说",
    chapterLength: { min: 1800, max: 3500, recommended: 2500 },
    updateFrequency: "日更",
    features: ["通俗易懂", "情节紧凑", "接地气"],
    tags: ["都市", "言情", "悬疑", "历史"],
    requirements: "语言通俗易懂，贴近大众阅读习惯"
  },
  zongheng: {
    name: "纵横中文网",
    chapterLength: { min: 3000, max: 5000, recommended: 3800 },
    updateFrequency: "日更",
    features: ["男频向", "爽文", "升级流"],
    tags: ["玄幻", "都市", "历史", "军事"],
    requirements: "男性向爽文，注重升级体系和战斗描写"
  },
  k17: {
    name: "17K小说网",
    chapterLength: { min: 2500, max: 4500, recommended: 3200 },
    updateFrequency: "日更",
    features: ["多元化", "包容性强", "题材丰富"],
    tags: ["玄幻", "都市", "言情", "科幻", "历史"],
    requirements: "题材包容性强，注重故事性和可读性"
  },
  feilu: {
    name: "飞卢小说网",
    chapterLength: { min: 2000, max: 3500, recommended: 2800 },
    updateFrequency: "日更或多更",
    features: ["同人文", "二次元", "轻小说"],
    tags: ["同人", "二次元", "轻小说", "游戏"],
    requirements: "适合同人创作和二次元题材，节奏要快"
  }
};
```

#### 5.1.4 技术实现
```typescript
// 大纲生成服务接口
interface OutlineGenerationService {
  generateOutline(params: OutlineParams): Promise<OutlineResult>;
  saveOutline(outline: Outline): Promise<void>;
  clearOutline(projectId: string): Promise<void>;
}

// 大纲参数接口
interface OutlineParams {
  title: string;           // 小说标题
  genre: string;           // 小说类型
  theme: string;           // 小说主题
  style: string;           // 小说风格
  chapterCount: number;    // 章节数 (1-9999)
  wordsPerChapter: number; // 每章字数 (200-9999)
  startChapter: number;    // 起始章节
  endChapter: number;      // 结束章节
  mainCharacters: number;  // 主角数量
  importantCharacters: number; // 重要角色数量
  supportingCharacters: number; // 配角数量
  villainCharacters: number; // 反派数量
  extraCharacters: number; // 龙套数量
  aiModel: string;         // 选择的AI模型
  promptTemplate: string;  // 提示词模板
}
```



### 5.2 大纲编辑模块

#### 5.2.1 功能概述
大纲编辑模块允许用户对AI生成的大纲进行精细化编辑和优化，支持AI辅助编辑功能。

#### 5.2.2 核心功能
- **标题编辑**: 支持手动编辑和AI重新生成小说标题
- **主题编辑**: 中心思想的编辑和AI优化
- **梗概编辑**: 故事梗概的详细编辑和扩展
- **世界观编辑**: 世界观设定的完善和AI辅助生成
- **保存管理**: 编辑内容的实时保存和版本管理

#### 5.2.3 技术实现
```typescript
// 大纲编辑服务接口
interface OutlineEditService {
  // 获取大纲数据
  getOutline(projectId: string): Promise<OutlineData>;

  // 更新大纲字段
  updateOutlineField(projectId: string, field: string, value: string): Promise<void>;

  // AI辅助编辑
  aiAssistEdit(projectId: string, field: string, prompt: string): Promise<string>;

  // 保存大纲
  saveOutline(projectId: string, outline: OutlineData): Promise<void>;

  // 版本管理
  createVersion(projectId: string, description: string): Promise<string>;
  getVersionHistory(projectId: string): Promise<OutlineVersion[]>;
  restoreVersion(projectId: string, versionId: string): Promise<void>;
}

// 大纲数据结构
interface OutlineData {
  id: string;
  title: string;
  theme: string;
  summary: string;
  worldview: string;
  genre: string;
  style: string;
  chapterCount: number;
  targetWordCount: number;
  updatedAt: Date;
  version: number;
}

// 版本历史
interface OutlineVersion {
  id: string;
  version: number;
  description: string;
  data: OutlineData;
  createdAt: Date;
}

// 实时保存管理
class OutlineAutoSave {
  private saveTimer: NodeJS.Timeout | null = null;
  private readonly SAVE_DELAY = 2000; // 2秒延迟保存

  scheduleAutoSave(projectId: string, outline: OutlineData) {
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
    }

    this.saveTimer = setTimeout(async () => {
      await this.outlineService.saveOutline(projectId, outline);
      this.saveTimer = null;
    }, this.SAVE_DELAY);
  }

  forceSave(projectId: string, outline: OutlineData) {
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
      this.saveTimer = null;
    }
    return this.outlineService.saveOutline(projectId, outline);
  }
}
```

### 5.3 章节编辑模块

#### 5.3.1 功能概述
章节编辑模块用于管理小说的章节结构，包括章节的添加、删除、排序和基本信息编辑。

#### 5.3.2 核心功能
- **章节列表管理**: 显示所有章节，支持拖拽排序
- **章节添加**: 手动添加新章节或基于大纲自动生成
- **章节信息编辑**: 章节标题、摘要的编辑
- **角色关联**: 为章节关联相关角色
- **AI辅助生成**: 使用AI生成章节标题和摘要

#### 5.3.3 技术实现
```typescript
// 章节编辑服务接口
interface ChapterEditService {
  // 获取章节列表
  getChapterList(projectId: string): Promise<ChapterInfo[]>;

  // 添加章节
  addChapter(projectId: string, chapter: Partial<ChapterInfo>): Promise<string>;

  // 更新章节信息
  updateChapter(projectId: string, chapterId: string, updates: Partial<ChapterInfo>): Promise<void>;

  // 删除章节
  deleteChapter(projectId: string, chapterId: string): Promise<void>;

  // 章节排序
  reorderChapters(projectId: string, chapterIds: string[]): Promise<void>;

  // AI生成章节标题和摘要
  aiGenerateChapterInfo(projectId: string, chapterIndex: number): Promise<{title: string, summary: string}>;

  // 关联角色
  associateCharacters(projectId: string, chapterId: string, characterIds: string[]): Promise<void>;
}

// 章节信息数据结构
interface ChapterInfo {
  id: string;
  projectId: string;
  index: number;
  title: string;
  summary: string;
  content: string;
  wordCount: number;
  status: 'draft' | 'writing' | 'completed';
  associatedCharacters: string[];
  createdAt: Date;
  updatedAt: Date;
}

// 拖拽排序管理
class ChapterDragManager {
  private draggedChapter: ChapterInfo | null = null;

  startDrag(chapter: ChapterInfo) {
    this.draggedChapter = chapter;
  }

  async drop(targetIndex: number, projectId: string) {
    if (!this.draggedChapter) return;

    const chapters = await this.chapterService.getChapterList(projectId);
    const reorderedChapters = this.reorderArray(chapters, this.draggedChapter.index, targetIndex);

    // 更新章节索引
    const chapterIds = reorderedChapters.map(chapter => chapter.id);
    await this.chapterService.reorderChapters(projectId, chapterIds);

    this.draggedChapter = null;
  }

  private reorderArray<T>(array: T[], fromIndex: number, toIndex: number): T[] {
    const result = [...array];
    const [removed] = result.splice(fromIndex, 1);
    result.splice(toIndex, 0, removed);
    return result;
  }
}

// AI辅助生成管理
class ChapterAIAssistant {
  async generateChapterInfo(projectId: string, chapterIndex: number): Promise<{title: string, summary: string}> {
    // 获取项目上下文
    const outline = await this.outlineService.getOutline(projectId);
    const previousChapters = await this.chapterService.getChapterList(projectId);

    // 构建提示词
    const prompt = this.buildChapterPrompt(outline, previousChapters, chapterIndex);

    // 调用AI生成
    const response = await this.aiService.generate(prompt);

    return this.parseChapterResponse(response);
  }

  private buildChapterPrompt(outline: OutlineData, chapters: ChapterInfo[], index: number): string {
    return `基于以下小说大纲和已有章节，为第${index + 1}章生成标题和摘要：

大纲信息：
标题：${outline.title}
主题：${outline.theme}
梗概：${outline.summary}

已有章节：
${chapters.map(ch => `第${ch.index + 1}章：${ch.title} - ${ch.summary}`).join('\n')}

请生成第${index + 1}章的：
1. 章节标题
2. 章节摘要（100-200字）

要求：
- 标题简洁有力，体现本章重点
- 摘要概括本章主要情节和发展
- 与前面章节保持连贯性
- 推进整体故事发展`;
  }

  private parseChapterResponse(response: string): {title: string, summary: string} {
    // 解析AI响应，提取标题和摘要
    const lines = response.split('\n').filter(line => line.trim());
    let title = '';
    let summary = '';

    for (const line of lines) {
      if (line.includes('标题') || line.includes('章节标题')) {
        title = line.replace(/.*[:：]/, '').trim();
      } else if (line.includes('摘要') || line.includes('章节摘要')) {
        summary = line.replace(/.*[:：]/, '').trim();
      }
    }

    return { title: title || '未命名章节', summary: summary || '暂无摘要' };
  }
}
```

### 5.4 章节生成模块

#### 5.4.1 功能概述
章节生成模块是核心的内容创作功能，通过AI模型根据大纲和上下文生成具体的章节内容。

#### 5.4.2 核心功能
- **章节选择**: 选择要生成内容的章节
- **上下文管理**: 自动提取前后章节的上下文信息
- **AI内容生成**: 基于大纲、角色、上下文生成章节内容
- **目标字数控制**: 设置生成内容的目标字数
- **内容编辑**: 富文本编辑器支持内容修改
- **AI辅助编辑**: 选定文本的润色和优化
- **降AI味处理**: 自动优化AI生成内容的表达方式

#### 5.4.3 技术实现
```typescript
// 章节生成服务接口
interface ChapterGenerationService {
  generateChapter(params: ChapterGenerationParams): Promise<string>;
  polishText(text: string, style: string): Promise<string>;
  reduceAIFlavor(content: string): Promise<string>;
}

// 章节生成参数
interface ChapterGenerationParams {
  projectId: string;
  chapterNumber: number;
  targetWordCount?: number;
  selectedCharacters: string[];
  contextChapters: number[];
  aiModel: string;
  writingStyle: string;
}
```



### 5.5 人物编辑模块

#### 5.5.1 功能概述
人物编辑模块用于管理小说中的所有角色，包括主角、重要角色、配角、反派和龙套的详细信息管理。

#### 5.5.2 核心功能
- **角色列表管理**: 显示所有角色，支持分类筛选
- **角色信息编辑**: 姓名、身份、性格、背景等详细信息
- **AI角色生成**: 使用AI自动生成角色设定
- **角色分类**: 主角、重要角色、配角、反派、龙套的分类管理
- **角色搜索**: 快速查找特定角色

#### 5.5.3 技术实现
```typescript
// 角色编辑服务接口
interface CharacterEditService {
  // 获取角色列表
  getCharacterList(projectId: string, category?: CharacterCategory): Promise<Character[]>;

  // 创建角色
  createCharacter(projectId: string, character: Partial<Character>): Promise<string>;

  // 更新角色信息
  updateCharacter(projectId: string, characterId: string, updates: Partial<Character>): Promise<void>;

  // 删除角色
  deleteCharacter(projectId: string, characterId: string): Promise<void>;

  // AI生成角色
  aiGenerateCharacter(projectId: string, prompt: string): Promise<Character>;

  // 搜索角色
  searchCharacters(projectId: string, query: string): Promise<Character[]>;
}

// 角色数据结构
interface Character {
  id: string;
  projectId: string;
  name: string;
  category: CharacterCategory;
  avatar?: string;

  // 基本信息
  age: number;
  gender: 'male' | 'female' | 'other';
  occupation: string;
  identity: string;

  // 外貌特征
  appearance: {
    height: string;
    build: string;
    hairColor: string;
    eyeColor: string;
    distinctiveFeatures: string;
  };

  // 性格特征
  personality: {
    traits: string[];
    strengths: string[];
    weaknesses: string[];
    fears: string[];
    motivations: string[];
  };

  // 背景故事
  background: {
    birthplace: string;
    family: string;
    education: string;
    pastExperiences: string;
    currentSituation: string;
  };

  // 角色弧线
  characterArc: {
    initialState: string;
    goal: string;
    obstacles: string[];
    growth: string;
    finalState: string;
  };

  // 关系信息
  relationships: CharacterRelationship[];

  createdAt: Date;
  updatedAt: Date;
}

// 角色分类
enum CharacterCategory {
  PROTAGONIST = 'protagonist',      // 主角
  MAJOR = 'major',                 // 重要角色
  SUPPORTING = 'supporting',       // 配角
  ANTAGONIST = 'antagonist',       // 反派
  MINOR = 'minor'                  // 龙套
}

// 角色关系
interface CharacterRelationship {
  targetCharacterId: string;
  relationshipType: RelationshipType;
  description: string;
  intimacyLevel: number; // 1-10
}

enum RelationshipType {
  FAMILY = 'family',
  FRIEND = 'friend',
  ENEMY = 'enemy',
  LOVER = 'lover',
  COLLEAGUE = 'colleague',
  MENTOR = 'mentor',
  STUDENT = 'student',
  RIVAL = 'rival',
  STRANGER = 'stranger'
}

// AI角色生成器
class CharacterAIGenerator {
  async generateCharacter(projectId: string, prompt: string): Promise<Character> {
    // 获取项目上下文
    const outline = await this.outlineService.getOutline(projectId);
    const existingCharacters = await this.characterService.getCharacterList(projectId);

    // 构建生成提示词
    const fullPrompt = this.buildCharacterPrompt(outline, existingCharacters, prompt);

    // 调用AI生成
    const response = await this.aiService.generate(fullPrompt);

    // 解析响应
    return this.parseCharacterResponse(response, projectId);
  }

  private buildCharacterPrompt(outline: OutlineData, characters: Character[], userPrompt: string): string {
    return `基于以下小说设定，生成一个角色：

小说信息：
- 标题：${outline.title}
- 类型：${outline.genre}
- 主题：${outline.theme}
- 世界观：${outline.worldview}

已有角色：
${characters.map(char => `- ${char.name}（${char.category}）：${char.identity}`).join('\n')}

用户要求：${userPrompt}

请生成角色的详细信息，包括：
1. 基本信息（姓名、年龄、性别、职业、身份）
2. 外貌特征
3. 性格特征（优点、缺点、恐惧、动机）
4. 背景故事
5. 角色发展弧线

要求：
- 角色要符合小说的世界观和风格
- 与已有角色形成互补或冲突
- 具有独特性和可发展性`;
  }

  private parseCharacterResponse(response: string, projectId: string): Character {
    // 这里实现AI响应的解析逻辑
    // 将AI生成的文本解析为Character对象
    // 实际实现中需要更复杂的解析逻辑

    return {
      id: this.generateId(),
      projectId,
      name: '新角色',
      category: CharacterCategory.SUPPORTING,
      age: 25,
      gender: 'other',
      occupation: '',
      identity: '',
      appearance: {
        height: '',
        build: '',
        hairColor: '',
        eyeColor: '',
        distinctiveFeatures: ''
      },
      personality: {
        traits: [],
        strengths: [],
        weaknesses: [],
        fears: [],
        motivations: []
      },
      background: {
        birthplace: '',
        family: '',
        education: '',
        pastExperiences: '',
        currentSituation: ''
      },
      characterArc: {
        initialState: '',
        goal: '',
        obstacles: [],
        growth: '',
        finalState: ''
      },
      relationships: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

// 角色搜索管理
class CharacterSearchManager {
  searchCharacters(characters: Character[], query: string): Character[] {
    const lowercaseQuery = query.toLowerCase();

    return characters.filter(character => {
      // 搜索姓名
      if (character.name.toLowerCase().includes(lowercaseQuery)) return true;

      // 搜索身份
      if (character.identity.toLowerCase().includes(lowercaseQuery)) return true;

      // 搜索职业
      if (character.occupation.toLowerCase().includes(lowercaseQuery)) return true;

      // 搜索性格特征
      if (character.personality.traits.some(trait =>
        trait.toLowerCase().includes(lowercaseQuery))) return true;

      return false;
    });
  }

  filterByCategory(characters: Character[], category: CharacterCategory): Character[] {
    return characters.filter(character => character.category === category);
  }
}
```

### 5.6 人物关系图模块

#### 5.6.1 功能概述
人物关系图模块提供可视化的角色关系管理，帮助作者理清复杂的人物关系网络。

#### 5.6.2 核心功能
- **关系图可视化**: 以图形方式展示角色间的关系
- **关系类型管理**: 朋友、敌人、父子、恋人等关系类型
- **关系编辑**: 添加、修改、删除角色间的关系
- **关系描述**: 为每个关系添加详细描述
- **关系暂存**: 支持关系的临时保存和批量提交功能
- **图形交互**: 支持拖拽、缩放、节点选择等交互操作

#### 5.6.3 技术实现
```typescript
// 关系图服务接口
interface RelationshipGraphService {
  // 获取关系图数据
  getRelationshipGraph(projectId: string): Promise<RelationshipGraph>;

  // 添加关系
  addRelationship(projectId: string, relationship: CharacterRelationship): Promise<void>;

  // 更新关系
  updateRelationship(projectId: string, relationshipId: string, updates: Partial<CharacterRelationship>): Promise<void>;

  // 删除关系
  deleteRelationship(projectId: string, relationshipId: string): Promise<void>;

  // 批量保存关系
  batchSaveRelationships(projectId: string, relationships: CharacterRelationship[]): Promise<void>;

  // 分析关系网络
  analyzeRelationshipNetwork(projectId: string): Promise<NetworkAnalysis>;
}

// 关系图数据结构
interface RelationshipGraph {
  nodes: GraphNode[];
  edges: GraphEdge[];
  layout: GraphLayout;
}

interface GraphNode {
  id: string;
  characterId: string;
  name: string;
  category: CharacterCategory;
  position: { x: number; y: number };
  size: number;
  color: string;
  avatar?: string;
}

interface GraphEdge {
  id: string;
  sourceId: string;
  targetId: string;
  relationshipType: RelationshipType;
  description: string;
  intimacyLevel: number;
  color: string;
  width: number;
  style: 'solid' | 'dashed' | 'dotted';
}

interface GraphLayout {
  algorithm: 'force' | 'circular' | 'hierarchical' | 'grid';
  parameters: {
    nodeSpacing: number;
    edgeLength: number;
    repulsion: number;
    attraction: number;
  };
}

// 关系图可视化引擎
class RelationshipGraphEngine {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private graph: RelationshipGraph;
  private selectedNode: GraphNode | null = null;
  private isDragging = false;
  private dragOffset = { x: 0, y: 0 };
  private scale = 1;
  private offset = { x: 0, y: 0 };

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.setupEventListeners();
  }

  loadGraph(graph: RelationshipGraph) {
    this.graph = graph;
    this.render();
  }

  private setupEventListeners() {
    // 鼠标事件
    this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.addEventListener('wheel', this.onWheel.bind(this));

    // 触摸事件（移动端支持）
    this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this));
    this.canvas.addEventListener('touchend', this.onTouchEnd.bind(this));
  }

  private onMouseDown(event: MouseEvent) {
    const rect = this.canvas.getBoundingClientRect();
    const x = (event.clientX - rect.left - this.offset.x) / this.scale;
    const y = (event.clientY - rect.top - this.offset.y) / this.scale;

    const clickedNode = this.findNodeAt(x, y);
    if (clickedNode) {
      this.selectedNode = clickedNode;
      this.isDragging = true;
      this.dragOffset = {
        x: x - clickedNode.position.x,
        y: y - clickedNode.position.y
      };
    }
  }

  private onMouseMove(event: MouseEvent) {
    if (this.isDragging && this.selectedNode) {
      const rect = this.canvas.getBoundingClientRect();
      const x = (event.clientX - rect.left - this.offset.x) / this.scale;
      const y = (event.clientY - rect.top - this.offset.y) / this.scale;

      this.selectedNode.position.x = x - this.dragOffset.x;
      this.selectedNode.position.y = y - this.dragOffset.y;

      this.render();
    }
  }

  private onMouseUp() {
    this.isDragging = false;
    this.selectedNode = null;
  }

  private onWheel(event: WheelEvent) {
    event.preventDefault();
    const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1;
    this.scale *= scaleFactor;
    this.scale = Math.max(0.1, Math.min(3, this.scale));
    this.render();
  }

  private findNodeAt(x: number, y: number): GraphNode | null {
    return this.graph.nodes.find(node => {
      const dx = x - node.position.x;
      const dy = y - node.position.y;
      return Math.sqrt(dx * dx + dy * dy) <= node.size;
    }) || null;
  }

  private render() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.ctx.save();

    // 应用变换
    this.ctx.translate(this.offset.x, this.offset.y);
    this.ctx.scale(this.scale, this.scale);

    // 绘制边
    this.graph.edges.forEach(edge => this.drawEdge(edge));

    // 绘制节点
    this.graph.nodes.forEach(node => this.drawNode(node));

    this.ctx.restore();
  }

  private drawNode(node: GraphNode) {
    this.ctx.save();

    // 绘制节点圆形
    this.ctx.beginPath();
    this.ctx.arc(node.position.x, node.position.y, node.size, 0, 2 * Math.PI);
    this.ctx.fillStyle = node.color;
    this.ctx.fill();

    // 绘制边框
    this.ctx.strokeStyle = node === this.selectedNode ? '#ff6b6b' : '#333';
    this.ctx.lineWidth = node === this.selectedNode ? 3 : 1;
    this.ctx.stroke();

    // 绘制头像或文字
    if (node.avatar) {
      // 绘制头像逻辑
    } else {
      // 绘制文字
      this.ctx.fillStyle = '#333';
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(node.name, node.position.x, node.position.y);
    }

    this.ctx.restore();
  }

  private drawEdge(edge: GraphEdge) {
    const sourceNode = this.graph.nodes.find(n => n.id === edge.sourceId);
    const targetNode = this.graph.nodes.find(n => n.id === edge.targetId);

    if (!sourceNode || !targetNode) return;

    this.ctx.save();

    // 设置线条样式
    this.ctx.strokeStyle = edge.color;
    this.ctx.lineWidth = edge.width;

    if (edge.style === 'dashed') {
      this.ctx.setLineDash([5, 5]);
    } else if (edge.style === 'dotted') {
      this.ctx.setLineDash([2, 2]);
    }

    // 绘制线条
    this.ctx.beginPath();
    this.ctx.moveTo(sourceNode.position.x, sourceNode.position.y);
    this.ctx.lineTo(targetNode.position.x, targetNode.position.y);
    this.ctx.stroke();

    // 绘制箭头
    this.drawArrow(sourceNode.position, targetNode.position, edge.color);

    // 绘制关系标签
    const midX = (sourceNode.position.x + targetNode.position.x) / 2;
    const midY = (sourceNode.position.y + targetNode.position.y) / 2;

    this.ctx.fillStyle = '#666';
    this.ctx.font = '10px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.getRelationshipLabel(edge.relationshipType), midX, midY - 10);

    this.ctx.restore();
  }

  private drawArrow(from: {x: number, y: number}, to: {x: number, y: number}, color: string) {
    const angle = Math.atan2(to.y - from.y, to.x - from.x);
    const arrowLength = 10;
    const arrowAngle = Math.PI / 6;

    this.ctx.save();
    this.ctx.strokeStyle = color;
    this.ctx.fillStyle = color;

    this.ctx.beginPath();
    this.ctx.moveTo(to.x, to.y);
    this.ctx.lineTo(
      to.x - arrowLength * Math.cos(angle - arrowAngle),
      to.y - arrowLength * Math.sin(angle - arrowAngle)
    );
    this.ctx.lineTo(
      to.x - arrowLength * Math.cos(angle + arrowAngle),
      to.y - arrowLength * Math.sin(angle + arrowAngle)
    );
    this.ctx.closePath();
    this.ctx.fill();

    this.ctx.restore();
  }

  private getRelationshipLabel(type: RelationshipType): string {
    const labels = {
      [RelationshipType.FAMILY]: '家人',
      [RelationshipType.FRIEND]: '朋友',
      [RelationshipType.ENEMY]: '敌人',
      [RelationshipType.LOVER]: '恋人',
      [RelationshipType.COLLEAGUE]: '同事',
      [RelationshipType.MENTOR]: '师父',
      [RelationshipType.STUDENT]: '学生',
      [RelationshipType.RIVAL]: '对手',
      [RelationshipType.STRANGER]: '陌生人'
    };
    return labels[type] || '未知';
  }
}

// 关系网络分析
interface NetworkAnalysis {
  centralCharacters: string[]; // 中心角色
  isolatedCharacters: string[]; // 孤立角色
  relationshipDensity: number; // 关系密度
  clusterGroups: CharacterCluster[]; // 角色群组
  suggestions: string[]; // 改进建议
}

interface CharacterCluster {
  characters: string[];
  relationshipType: RelationshipType;
  strength: number;
}

class RelationshipAnalyzer {
  analyzeNetwork(graph: RelationshipGraph): NetworkAnalysis {
    const centralCharacters = this.findCentralCharacters(graph);
    const isolatedCharacters = this.findIsolatedCharacters(graph);
    const relationshipDensity = this.calculateDensity(graph);
    const clusterGroups = this.findClusters(graph);
    const suggestions = this.generateSuggestions(graph, {
      centralCharacters,
      isolatedCharacters,
      relationshipDensity,
      clusterGroups
    });

    return {
      centralCharacters,
      isolatedCharacters,
      relationshipDensity,
      clusterGroups,
      suggestions
    };
  }

  private findCentralCharacters(graph: RelationshipGraph): string[] {
    const connectionCounts = new Map<string, number>();

    graph.edges.forEach(edge => {
      connectionCounts.set(edge.sourceId, (connectionCounts.get(edge.sourceId) || 0) + 1);
      connectionCounts.set(edge.targetId, (connectionCounts.get(edge.targetId) || 0) + 1);
    });

    const sorted = Array.from(connectionCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);

    return sorted.map(([characterId]) => characterId);
  }

  private findIsolatedCharacters(graph: RelationshipGraph): string[] {
    const connectedCharacters = new Set<string>();

    graph.edges.forEach(edge => {
      connectedCharacters.add(edge.sourceId);
      connectedCharacters.add(edge.targetId);
    });

    return graph.nodes
      .filter(node => !connectedCharacters.has(node.characterId))
      .map(node => node.characterId);
  }

  private calculateDensity(graph: RelationshipGraph): number {
    const nodeCount = graph.nodes.length;
    const edgeCount = graph.edges.length;
    const maxPossibleEdges = nodeCount * (nodeCount - 1) / 2;

    return maxPossibleEdges > 0 ? edgeCount / maxPossibleEdges : 0;
  }

  private findClusters(graph: RelationshipGraph): CharacterCluster[] {
    // 实现聚类算法，找出关系密切的角色群组
    // 这里简化实现
    return [];
  }

  private generateSuggestions(graph: RelationshipGraph, analysis: Partial<NetworkAnalysis>): string[] {
    const suggestions: string[] = [];

    if (analysis.isolatedCharacters && analysis.isolatedCharacters.length > 0) {
      suggestions.push(`发现${analysis.isolatedCharacters.length}个孤立角色，建议为他们添加关系连接`);
    }

    if (analysis.relationshipDensity && analysis.relationshipDensity < 0.3) {
      suggestions.push('角色关系网络较为稀疏，建议增加更多角色间的互动');
    }

    if (analysis.centralCharacters && analysis.centralCharacters.length < 2) {
      suggestions.push('缺少足够的中心角色，建议增强某些角色的重要性');
    }

    return suggestions;
  }
}
```

### 5.7 章节分析模块

#### 5.7.1 功能概述
章节分析模块使用AI对已完成的章节进行深度分析，提供改进建议和质量评估。

#### 5.7.2 核心功能
- **章节选择**: 选择要分析的章节
- **多维度分析**: 核心剧情、故事梗概、优缺点、角色标注、物品标注
- **改进建议**: AI提供具体的改进建议
- **章节改进**: 基于分析结果自动优化章节内容
- **分析报告**: 生成详细的分析报告

#### 5.7.3 技术实现
```typescript
// 章节分析服务接口
interface ChapterAnalysisService {
  // 分析章节
  analyzeChapter(projectId: string, chapterId: string): Promise<ChapterAnalysis>;

  // 获取分析历史
  getAnalysisHistory(projectId: string, chapterId: string): Promise<ChapterAnalysis[]>;

  // 应用改进建议
  applyImprovements(projectId: string, chapterId: string, improvements: string[]): Promise<string>;

  // 生成分析报告
  generateAnalysisReport(projectId: string, chapterId: string): Promise<AnalysisReport>;
}

// 章节分析结果
interface ChapterAnalysis {
  id: string;
  chapterId: string;
  analysisDate: Date;

  // 核心剧情分析
  plotAnalysis: {
    mainEvents: string[];
    plotProgression: 'excellent' | 'good' | 'average' | 'poor';
    pacing: 'too-fast' | 'fast' | 'good' | 'slow' | 'too-slow';
    tension: number; // 1-10
    climax: string;
  };

  // 故事梗概
  summary: {
    brief: string;
    detailed: string;
    keyPoints: string[];
  };

  // 优缺点分析
  strengths: string[];
  weaknesses: string[];

  // 角色分析
  characterAnalysis: {
    presentCharacters: CharacterPresence[];
    characterDevelopment: string[];
    dialogueQuality: 'excellent' | 'good' | 'average' | 'poor';
  };

  // 物品/道具标注
  items: {
    name: string;
    description: string;
    importance: 'high' | 'medium' | 'low';
    firstMention: boolean;
  }[];

  // 改进建议
  improvements: ImprovementSuggestion[];

  // 整体评分
  overallScore: number; // 1-100

  // AI模型信息
  aiModel: string;
  analysisVersion: string;
}

interface CharacterPresence {
  characterId: string;
  characterName: string;
  screenTime: 'major' | 'moderate' | 'minor';
  development: string;
  interactions: string[];
}

interface ImprovementSuggestion {
  category: 'plot' | 'character' | 'dialogue' | 'description' | 'pacing';
  priority: 'high' | 'medium' | 'low';
  suggestion: string;
  example?: string;
  autoFixAvailable: boolean;
}

interface AnalysisReport {
  chapterInfo: {
    title: string;
    wordCount: number;
    analysisDate: Date;
  };
  executiveSummary: string;
  detailedAnalysis: ChapterAnalysis;
  recommendations: string[];
  comparisonWithPreviousChapters?: ComparisonData;
}

interface ComparisonData {
  averageScore: number;
  trendAnalysis: 'improving' | 'stable' | 'declining';
  consistencyIssues: string[];
}

// 章节分析引擎
class ChapterAnalysisEngine {
  async analyzeChapter(chapter: ChapterInfo, context: AnalysisContext): Promise<ChapterAnalysis> {
    // 构建分析提示词
    const prompt = this.buildAnalysisPrompt(chapter, context);

    // 调用AI进行分析
    const response = await this.aiService.generate(prompt);

    // 解析分析结果
    return this.parseAnalysisResponse(response, chapter.id);
  }

  private buildAnalysisPrompt(chapter: ChapterInfo, context: AnalysisContext): string {
    return `请对以下小说章节进行深度分析：

章节信息：
标题：${chapter.title}
字数：${chapter.wordCount}
摘要：${chapter.summary}

章节内容：
${chapter.content}

小说背景：
${context.outline ? `大纲：${context.outline.summary}` : ''}
${context.characters ? `主要角色：${context.characters.map(c => c.name).join('、')}` : ''}

请从以下维度进行分析：

1. 核心剧情分析
   - 主要事件和情节发展
   - 节奏控制（过快/适中/过慢）
   - 紧张感和冲突设置
   - 高潮部分识别

2. 角色表现分析
   - 出场角色及其表现
   - 角色发展和成长
   - 对话质量和真实性
   - 角色互动效果

3. 写作技巧分析
   - 描写手法运用
   - 语言表达质量
   - 情感渲染效果
   - 细节处理水平

4. 优缺点总结
   - 本章节的亮点
   - 需要改进的地方
   - 具体的修改建议

5. 物品道具标注
   - 重要物品的出现
   - 道具的作用和意义

请提供详细、具体、可操作的分析结果。`;
  }

  private parseAnalysisResponse(response: string, chapterId: string): ChapterAnalysis {
    // 实际实现中需要更复杂的解析逻辑
    // 这里提供基本结构

    return {
      id: this.generateId(),
      chapterId,
      analysisDate: new Date(),
      plotAnalysis: {
        mainEvents: [],
        plotProgression: 'good',
        pacing: 'good',
        tension: 7,
        climax: ''
      },
      summary: {
        brief: '',
        detailed: '',
        keyPoints: []
      },
      strengths: [],
      weaknesses: [],
      characterAnalysis: {
        presentCharacters: [],
        characterDevelopment: [],
        dialogueQuality: 'good'
      },
      items: [],
      improvements: [],
      overallScore: 75,
      aiModel: 'gpt-4',
      analysisVersion: '1.0'
    };
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

interface AnalysisContext {
  outline?: OutlineData;
  characters?: Character[];
  previousChapters?: ChapterInfo[];
  projectSettings?: any;
}

// 改进建议应用器
class ImprovementApplicator {
  async applyImprovements(chapter: ChapterInfo, improvements: ImprovementSuggestion[]): Promise<string> {
    let improvedContent = chapter.content;

    for (const improvement of improvements) {
      if (improvement.autoFixAvailable) {
        improvedContent = await this.applyAutoFix(improvedContent, improvement);
      }
    }

    return improvedContent;
  }

  private async applyAutoFix(content: string, improvement: ImprovementSuggestion): Promise<string> {
    const prompt = `请根据以下改进建议优化文本内容：

原文：
${content}

改进建议：
${improvement.suggestion}

请返回优化后的文本，保持原有的故事情节和结构，只改进表达方式。`;

    const response = await this.aiService.generate(prompt);
    return response;
  }
}
```

### 5.8 统计信息模块

#### 5.8.1 功能概述
统计信息模块提供小说创作进度的实时统计和数据分析，帮助作者了解创作状态。

#### 5.8.2 核心功能
- **概览统计**: 小说标题、章节数、总字数、平均每章字数、完成度
- **章节统计**: 每章的详细字数统计和状态
- **进度跟踪**: 创作进度的可视化展示
- **数据刷新**: 实时更新统计数据
- **导出功能**: 统计报告的导出

#### 5.8.3 技术实现
```typescript
// 统计信息服务接口
interface StatisticsService {
  // 获取项目统计
  getProjectStatistics(projectId: string): Promise<ProjectStatistics>;

  // 获取章节统计
  getChapterStatistics(projectId: string): Promise<ChapterStatistics[]>;

  // 获取进度统计
  getProgressStatistics(projectId: string): Promise<ProgressStatistics>;

  // 刷新统计数据
  refreshStatistics(projectId: string): Promise<void>;

  // 导出统计报告
  exportStatisticsReport(projectId: string, format: 'pdf' | 'excel' | 'json'): Promise<Blob>;

  // 获取历史统计
  getHistoricalStatistics(projectId: string, dateRange: DateRange): Promise<HistoricalStatistics>;
}

// 项目统计数据
interface ProjectStatistics {
  projectId: string;
  projectTitle: string;

  // 基本统计
  totalChapters: number;
  completedChapters: number;
  totalWordCount: number;
  averageWordsPerChapter: number;

  // 完成度统计
  completionPercentage: number;
  estimatedTotalWords: number;
  remainingWords: number;

  // 时间统计
  createdAt: Date;
  lastUpdated: Date;
  totalWritingDays: number;
  averageWordsPerDay: number;

  // 角色统计
  totalCharacters: number;
  charactersByCategory: {
    protagonist: number;
    major: number;
    supporting: number;
    antagonist: number;
    minor: number;
  };

  // 质量统计
  averageChapterScore: number;
  analysisCount: number;
  improvementsSuggested: number;
  improvementsApplied: number;

  lastCalculated: Date;
}

// 章节统计数据
interface ChapterStatistics {
  chapterId: string;
  chapterIndex: number;
  title: string;

  // 字数统计
  wordCount: number;
  characterCount: number;
  paragraphCount: number;
  sentenceCount: number;

  // 状态信息
  status: 'draft' | 'writing' | 'completed';
  completionPercentage: number;

  // 质量指标
  analysisScore?: number;
  readabilityScore?: number;

  // 时间信息
  createdAt: Date;
  lastModified: Date;
  writingTime: number; // 分钟

  // 角色出现
  charactersPresent: string[];

  // 修改历史
  revisionCount: number;
  lastRevisionDate?: Date;
}

// 进度统计数据
interface ProgressStatistics {
  // 整体进度
  overallProgress: {
    percentage: number;
    currentPhase: 'planning' | 'writing' | 'editing' | 'completed';
    estimatedCompletion: Date;
  };

  // 写作进度
  writingProgress: {
    dailyWordCounts: DailyWordCount[];
    weeklyProgress: WeeklyProgress[];
    monthlyProgress: MonthlyProgress[];
    writingStreak: number; // 连续写作天数
    longestStreak: number;
  };

  // 目标追踪
  goals: {
    dailyWordGoal: number;
    weeklyWordGoal: number;
    monthlyWordGoal: number;
    totalWordGoal: number;
    targetCompletionDate: Date;

    // 目标达成情况
    dailyGoalAchievement: number; // 百分比
    weeklyGoalAchievement: number;
    monthlyGoalAchievement: number;
  };

  // 效率统计
  productivity: {
    averageWordsPerSession: number;
    averageSessionDuration: number; // 分钟
    mostProductiveTimeOfDay: string;
    mostProductiveDayOfWeek: string;
  };
}

interface DailyWordCount {
  date: Date;
  wordCount: number;
  sessionCount: number;
  totalMinutes: number;
}

interface WeeklyProgress {
  weekStart: Date;
  weekEnd: Date;
  totalWords: number;
  chaptersCompleted: number;
  averageDailyWords: number;
}

interface MonthlyProgress {
  month: number;
  year: number;
  totalWords: number;
  chaptersCompleted: number;
  writingDays: number;
}

interface HistoricalStatistics {
  dateRange: DateRange;
  snapshots: StatisticsSnapshot[];
  trends: TrendAnalysis;
}

interface StatisticsSnapshot {
  date: Date;
  statistics: ProjectStatistics;
}

interface TrendAnalysis {
  wordCountTrend: 'increasing' | 'stable' | 'decreasing';
  productivityTrend: 'improving' | 'stable' | 'declining';
  qualityTrend: 'improving' | 'stable' | 'declining';
  predictions: {
    estimatedCompletionDate: Date;
    projectedFinalWordCount: number;
  };
}

interface DateRange {
  startDate: Date;
  endDate: Date;
}

// 统计计算引擎
class StatisticsCalculator {
  async calculateProjectStatistics(projectId: string): Promise<ProjectStatistics> {
    // 获取基础数据
    const chapters = await this.chapterService.getChapterList(projectId);
    const characters = await this.characterService.getCharacterList(projectId);
    const outline = await this.outlineService.getOutline(projectId);
    const analyses = await this.analysisService.getAnalysisHistory(projectId);

    // 计算基本统计
    const totalChapters = chapters.length;
    const completedChapters = chapters.filter(ch => ch.status === 'completed').length;
    const totalWordCount = chapters.reduce((sum, ch) => sum + ch.wordCount, 0);
    const averageWordsPerChapter = totalChapters > 0 ? totalWordCount / totalChapters : 0;

    // 计算完成度
    const completionPercentage = outline.chapterCount > 0 ?
      (completedChapters / outline.chapterCount) * 100 : 0;
    const estimatedTotalWords = outline.targetWordCount ||
      (outline.chapterCount * averageWordsPerChapter);
    const remainingWords = Math.max(0, estimatedTotalWords - totalWordCount);

    // 计算时间统计
    const createdAt = new Date(Math.min(...chapters.map(ch => ch.createdAt.getTime())));
    const lastUpdated = new Date(Math.max(...chapters.map(ch => ch.lastModified.getTime())));
    const totalWritingDays = this.calculateWritingDays(chapters);
    const averageWordsPerDay = totalWritingDays > 0 ? totalWordCount / totalWritingDays : 0;

    // 计算角色统计
    const charactersByCategory = this.calculateCharactersByCategory(characters);

    // 计算质量统计
    const qualityStats = this.calculateQualityStatistics(analyses);

    return {
      projectId,
      projectTitle: outline.title,
      totalChapters,
      completedChapters,
      totalWordCount,
      averageWordsPerChapter,
      completionPercentage,
      estimatedTotalWords,
      remainingWords,
      createdAt,
      lastUpdated,
      totalWritingDays,
      averageWordsPerDay,
      totalCharacters: characters.length,
      charactersByCategory,
      ...qualityStats,
      lastCalculated: new Date()
    };
  }

  private calculateWritingDays(chapters: ChapterInfo[]): number {
    const writingDates = new Set<string>();
    chapters.forEach(chapter => {
      const dateStr = chapter.lastModified.toISOString().split('T')[0];
      writingDates.add(dateStr);
    });
    return writingDates.size;
  }

  private calculateCharactersByCategory(characters: Character[]) {
    const counts = {
      protagonist: 0,
      major: 0,
      supporting: 0,
      antagonist: 0,
      minor: 0
    };

    characters.forEach(character => {
      switch (character.category) {
        case CharacterCategory.PROTAGONIST:
          counts.protagonist++;
          break;
        case CharacterCategory.MAJOR:
          counts.major++;
          break;
        case CharacterCategory.SUPPORTING:
          counts.supporting++;
          break;
        case CharacterCategory.ANTAGONIST:
          counts.antagonist++;
          break;
        case CharacterCategory.MINOR:
          counts.minor++;
          break;
      }
    });

    return counts;
  }

  private calculateQualityStatistics(analyses: ChapterAnalysis[]) {
    const scores = analyses.map(a => a.overallScore).filter(s => s > 0);
    const averageChapterScore = scores.length > 0 ?
      scores.reduce((sum, score) => sum + score, 0) / scores.length : 0;

    const improvementsSuggested = analyses.reduce(
      (sum, a) => sum + a.improvements.length, 0
    );

    // 这里需要从其他地方获取已应用的改进数量
    const improvementsApplied = 0; // 实际实现中需要从数据库获取

    return {
      averageChapterScore,
      analysisCount: analyses.length,
      improvementsSuggested,
      improvementsApplied
    };
  }
}

// 进度追踪器
class ProgressTracker {
  async trackWritingSession(projectId: string, wordCount: number, duration: number) {
    const session = {
      projectId,
      date: new Date(),
      wordCount,
      duration,
      timestamp: Date.now()
    };

    await this.saveWritingSession(session);
    await this.updateDailyProgress(projectId, session);
  }

  private async saveWritingSession(session: any) {
    // 保存写作会话数据
  }

  private async updateDailyProgress(projectId: string, session: any) {
    // 更新每日进度统计
  }

  async calculateWritingStreak(projectId: string): Promise<number> {
    const sessions = await this.getRecentSessions(projectId, 365); // 获取最近一年的会话

    let streak = 0;
    let currentDate = new Date();

    // 从今天开始往前计算连续写作天数
    while (true) {
      const dateStr = currentDate.toISOString().split('T')[0];
      const hasWritingOnDate = sessions.some(session =>
        session.date.toISOString().split('T')[0] === dateStr
      );

      if (hasWritingOnDate) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        break;
      }
    }

    return streak;
  }

  private async getRecentSessions(projectId: string, days: number) {
    // 获取最近指定天数的写作会话
    return [];
  }
}

// 报告生成器
class StatisticsReportGenerator {
  async generateReport(statistics: ProjectStatistics, format: 'pdf' | 'excel' | 'json'): Promise<Blob> {
    switch (format) {
      case 'json':
        return this.generateJSONReport(statistics);
      case 'excel':
        return this.generateExcelReport(statistics);
      case 'pdf':
        return this.generatePDFReport(statistics);
      default:
        throw new Error(`Unsupported format: ${format}`);
    }
  }

  private generateJSONReport(statistics: ProjectStatistics): Blob {
    const jsonData = JSON.stringify(statistics, null, 2);
    return new Blob([jsonData], { type: 'application/json' });
  }

  private async generateExcelReport(statistics: ProjectStatistics): Promise<Blob> {
    // 使用 xlsx 库生成 Excel 报告
    // 实际实现需要引入相应的库
    return new Blob([], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  }

  private async generatePDFReport(statistics: ProjectStatistics): Promise<Blob> {
    // 使用 jsPDF 或类似库生成 PDF 报告
    // 实际实现需要引入相应的库
    return new Blob([], { type: 'application/pdf' });
  }
}
```

### 5.9 AI聊天模块

#### 5.9.1 功能概述
AI聊天模块提供与AI模型的直接对话功能，用于验证AI模型是否正常工作，同时为用户提供写作咨询和创意讨论的平台。

#### 5.9.2 核心功能
- **模型验证**: 测试已配置的AI模型是否能正常响应
- **写作咨询**: 向AI询问写作技巧、情节建议等
- **创意讨论**: 与AI讨论角色设定、世界观构建等
- **实时对话**: 支持连续对话，保持上下文
- **对话历史**: 保存重要的对话记录
- **快速提问**: 内置常用写作问题模板

#### 5.9.3 技术实现设计

**对话管理系统**
```typescript
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  modelUsed: string;
  tokens?: number;
}

interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  modelConfig: AIModelConfig;
}

class ChatService {
  // 发送消息
  async sendMessage(content: string, sessionId: string): Promise<ChatMessage>;

  // 创建新会话
  async createSession(title: string): Promise<ChatSession>;

  // 保存对话历史
  async saveSession(session: ChatSession): Promise<void>;

  // 加载历史会话
  async loadSessions(): Promise<ChatSession[]>;

  // 清空会话
  async clearSession(sessionId: string): Promise<void>;
}
```



### 5.10 提示词模板系统

#### 5.10.1 功能概述
提示词模板系统提供内置和自定义的提示词模板，支持变量替换和模板管理，提升AI生成内容的质量和一致性。

#### 5.10.2 核心功能
- **内置模板库**: 预设多种专业提示词模板
- **自定义模板**: 用户可创建、编辑、删除个人模板
- **变量替换**: 支持动态变量替换功能
- **模板分类**: 按功能分类管理模板
- **模板导入导出**: 模板的备份和恢复
- **标点符号规范**: 严格遵循中英文标点符号使用规范

#### 5.10.3 标点符号使用规范
```javascript
const punctuationRules = {
  // 中文标点符号规范
  chinese: {
    period: "。",           // 句号
    comma: "，",            // 逗号
    question: "？",         // 问号
    exclamation: "！",      // 感叹号
    colon: "：",            // 冒号
    semicolon: "；",        // 分号
    quotationLeft: """,     // 左引号
    quotationRight: """,    // 右引号
    parenthesesLeft: "（",  // 左括号
    parenthesesRight: "）", // 右括号
    dash: "——",             // 破折号
    ellipsis: "……"          // 省略号
  },

  // 英文标点符号规范
  english: {
    period: ".",            // 句号
    comma: ",",             // 逗号
    question: "?",          // 问号
    exclamation: "!",       // 感叹号
    colon: ":",             // 冒号
    semicolon: ";",         // 分号
    quotationLeft: '"',     // 左引号
    quotationRight: '"',    // 右引号
    parenthesesLeft: "(",   // 左括号
    parenthesesRight: ")",  // 右括号
    dash: "--",             // 破折号
    ellipsis: "..."         // 省略号
  },

  // 混合使用规则
  mixedRules: {
    // 中文内容使用中文标点
    chineseContent: "中文内容必须使用中文标点符号",
    // 英文内容使用英文标点
    englishContent: "English content must use English punctuation",
    // 变量名使用英文
    variableNames: "模板变量名使用英文，如{title}、{genre}等",
    // 避免混用
    noMixing: "同一句话内不要混用中英文标点符号"
  }
};

// 标点符号检查函数
const checkPunctuation = (text) => {
  const issues = [];

  // 检查常见错误
  if (text.includes('。,') || text.includes(',。')) {
    issues.push('中英文逗号和句号混用');
  }

  if (text.includes('？?') || text.includes('?？')) {
    issues.push('中英文问号混用');
  }

  if (text.includes('！!') || text.includes('!！')) {
    issues.push('中英文感叹号混用');
  }

  return issues;
};
```

#### 5.10.4 内置模板分类
```javascript
const templateCategories = {
  outline: "大纲相关",
  chapter: "章节相关",
  character: "人设生成",
  worldview: "世界观设定",
  plot: "剧情线",
  writing: "写作技巧",
  optimization: "内容优化"
};
```

#### 5.10.5 标准大纲模板示例
```javascript
const outlineTemplate = {
  name: "标准大纲提示词",
  description: "标准的小说大纲生成提示词",
  category: "outline",
  content: `请为我创建一部小说的详细大纲，具体要求如下：

小说标题：{title}
小说类型：{genre}
主题：{theme}
风格：{style}

章节数：{chapterCount}章
每章字数：{wordsPerChapter}字

人物设置：
主角数量：{protagonistCount}个
重要角色数量：{importantCharacterCount}个
配角数量：{supportingCharacterCount}个
反派数量：{villainCharacterCount}个
龙套数量：{extraCharacterCount}个

生成范围：从第{startChapter}章 到 第{endChapter}章

请生成以下内容：
1. 小说标题
2. 核心主题
3. 主要人物（包括姓名、身份、性格特点和背景故事）
4. 故事梗概
5. 章节结构（每章包含标题、简介和具体章节）
6. 世界观设定

特别要求：
1. 章节标题必须包含章节号，如"第一章：章节标题"
2. 只生成指定范围内的章节，但保持与已有大纲的一致性

请确保大纲结构完整、逻辑合理，并以JSON格式返回。`,
  variables: [
    "title", "genre", "theme", "style", "chapterCount",
    "wordsPerChapter", "protagonistCount", "importantCharacterCount",
    "supportingCharacterCount", "extraCharacterCount",
    "startChapter", "endChapter"
  ]
};
```

#### 5.10.6 详细内置提示词模板库

**大纲相关模板**
```javascript
const outlineTemplates = {
  // 细纲生成模板
  detailedOutline: {
    name: "细纲生成提示词",
    category: "outline",
    content: `基于以下大纲信息，为第{chapterNumber}章生成详细的细纲：

章节标题：{chapterTitle}
章节简介：{chapterSummary}
相关角色：{characters}
前情提要：{previousSummary}

请生成包含以下内容的详细细纲：
1. 章节开头场景设置
2. 主要情节发展（3-5个关键情节点）
3. 角色互动和对话要点
4. 冲突设置和解决方案
5. 章节结尾和悬念设置
6. 与下一章的衔接点

要求：
- 情节发展要符合整体大纲逻辑
- 保持角色性格一致性
- 控制在{targetWords}字左右
- 符合{genre}类型小说的特点`
  },

  // 世界观设定模板
  worldBuilding: {
    name: "世界观设定提示词",
    category: "worldview",
    content: `为{genre}类型的小说《{title}》创建详细的世界观设定：

小说背景：{background}
主要设定：{mainSetting}

请生成以下世界观要素：
1. 世界基本设定（时代背景、地理环境、社会结构）
2. 力量体系（修炼等级、能力分类、成长路径）
3. 组织势力（主要门派、势力分布、权力关系）
4. 重要地点（关键场景、特殊区域、标志性建筑）
5. 文化背景（风俗习惯、价值观念、历史传承）
6. 特殊规则（世界法则、限制条件、独特机制）

要求：
- 设定要有逻辑性和一致性
- 符合{genre}类型的经典设定
- 为后续剧情发展留下空间
- 避免过于复杂难懂`
  }
};
```

**章节相关模板**
```javascript
const chapterTemplates = {
  // 章节生成模板
  chapterGeneration: {
    name: "章节内容生成",
    category: "chapter",
    content: `基于以下信息生成第{chapterNumber}章的完整内容：

章节标题：{chapterTitle}
目标字数：{targetWords}字
主要角色：{mainCharacters}
情节要点：{plotPoints}
前章回顾：{previousChapter}

写作要求：
1. 开头要有吸引力，快速进入情节
2. 保持节奏紧凑，避免拖沓
3. 对话要生动自然，符合角色性格
4. 环境描写适度，增强代入感
5. 结尾要有悬念或转折
6. 符合{style}的写作风格

特别注意：
- 保持与前文的连贯性
- 角色行为要符合设定
- 避免AI味过重的表达
- 情节发展要合理自然`
  },

  // 续写模板
  continuation: {
    name: "章节续写",
    category: "chapter",
    content: `请基于以下内容继续写作：

已有内容：
{existingContent}

续写要求：
1. 保持文风一致
2. 情节发展自然
3. 目标续写{continueWords}字
4. 保持角色性格一致
5. 推进主线剧情

请从上述内容的结尾处自然续写，不要重复已有内容。`
  },

  // 扩写模板
  expansion: {
    name: "内容扩写",
    category: "chapter",
    content: `请对以下内容进行扩写：

原始内容：
{originalContent}

扩写要求：
1. 在原有基础上增加{expandWords}字左右
2. 丰富细节描写（环境、动作、心理）
3. 增加对话内容，使角色更生动
4. 保持原有情节不变
5. 提升文章的可读性和代入感

扩写重点：
- 人物心理活动
- 环境氛围营造
- 动作细节描写
- 对话丰富化`
  }
};
```

**人设生成模板**
```javascript
const characterTemplates = {
  // 主角生成模板
  protagonist: {
    name: "主角人设生成",
    category: "character",
    content: `为{genre}类型小说创建主角人设：

基本要求：
- 小说类型：{genre}
- 角色性别：{gender}
- 年龄范围：{ageRange}
- 特殊要求：{specialRequirements}

请生成以下内容：
1. 基本信息（姓名、年龄、身份、职业）
2. 外貌特征（身高体型、面容特点、穿着风格）
3. 性格特点（主要性格、优缺点、行为习惯）
4. 背景故事（出身经历、重要事件、成长轨迹）
5. 能力设定（特殊能力、技能专长、成长潜力）
6. 人际关系（家庭背景、重要关系、社交圈子）
7. 目标动机（主要目标、内在动机、价值观念）

要求：
- 人设要有吸引力和成长空间
- 符合{genre}类型的主角特征
- 性格要有层次感，避免脸谱化
- 背景要为剧情发展服务`
  },

  // 配角生成模板
  supporting: {
    name: "配角人设生成",
    category: "character",
    content: `为小说创建配角人设：

角色定位：{roleType}（如：导师、对手、朋友、恋人等）
与主角关系：{relationshipWithMC}
重要程度：{importance}

请生成以下内容：
1. 基本信息（姓名、年龄、身份）
2. 外貌特征（突出特点即可）
3. 性格特点（与角色定位匹配）
4. 背景简介（关键经历）
5. 能力特长（专业技能）
6. 在剧情中的作用
7. 与主角的互动方式

要求：
- 人设要为剧情服务
- 性格要有特色，易于区分
- 避免与其他角色重复
- 符合角色定位的功能需求`
  }
};
```

**写作技巧模板**
```javascript
const writingTemplates = {
  // 润色模板
  polish: {
    name: "内容润色",
    category: "optimization",
    content: `请对以下文本进行润色优化：

原文：
{originalText}

润色要求：
1. 提升语言表达的流畅性
2. 增强文字的感染力
3. 优化句式结构，避免重复
4. 保持原意不变
5. 符合{style}的写作风格

重点优化：
- 词汇选择更精准
- 句式更有变化
- 节奏更加合适
- 减少AI味表达`
  },

  // 改写模板
  rewrite: {
    name: "内容改写",
    category: "optimization",
    content: `请对以下内容进行改写：

原文：
{originalText}

改写要求：
1. 保持核心情节不变
2. 改变表达方式和句式结构
3. 调整叙述角度（如需要）
4. 目标风格：{targetStyle}
5. 字数控制在{targetWords}字左右

改写重点：
- 语言风格转换
- 叙述方式调整
- 细节描写优化
- 整体节奏把控`
  },

  // 降AI味模板
  humanize: {
    name: "降AI味处理",
    category: "optimization",
    content: `请对以下AI生成的内容进行降AI味处理：

原文：
{aiGeneratedText}

处理要求：
1. 替换机械化的表达方式
2. 增加自然的语言变化
3. 减少重复的句式结构
4. 添加更多人性化的细节
5. 保持情节完整性

重点处理：
- 避免"然而"、"此外"等AI常用词
- 增加口语化表达
- 丰富情感描写
- 优化对话自然度
- 减少说教性语言`
  }
};
```

**特殊功能模板**
```javascript
const specialTemplates = {
  // 金手指生成模板
  goldenFinger: {
    name: "金手指设定生成",
    category: "plot",
    content: `为{genre}类型小说设计金手指系统：

小说背景：{background}
主角特点：{protagonistTraits}
期望类型：{goldenFingerType}

请设计包含以下要素的金手指：
1. 金手指名称和基本概念
2. 获得方式和触发条件
3. 基本功能和能力范围
4. 使用限制和代价
5. 升级成长机制
6. 与剧情的结合方式

设计原则：
- 要有新意，避免俗套
- 平衡性好，不能过于逆天
- 与主角性格匹配
- 为剧情发展服务
- 符合世界观设定`
  },

  // 黄金开篇模板
  goldenOpening: {
    name: "黄金开篇生成",
    category: "chapter",
    content: `为小说《{title}》创作吸引人的开篇：

小说信息：
- 类型：{genre}
- 主角：{protagonist}
- 背景：{setting}
- 核心冲突：{mainConflict}

开篇要求：
1. 前100字要抓住读者注意力
2. 快速建立代入感
3. 暗示主要冲突或悬念
4. 展现主角特点
5. 符合类型小说的期待

开篇策略：
- 动作开篇：直接进入紧张情节
- 对话开篇：通过对话展现冲突
- 悬念开篇：设置引人好奇的谜团
- 环境开篇：营造特殊的氛围

请生成800-1200字的精彩开篇。`
  },

  // 审稿模板
  review: {
    name: "内容审稿",
    category: "optimization",
    content: `请对以下章节内容进行专业审稿：

章节内容：
{chapterContent}

审稿维度：
1. 情节逻辑性（是否合理、有无漏洞）
2. 人物一致性（性格、行为是否符合设定）
3. 语言表达（流畅度、准确性、风格统一）
4. 节奏把控（快慢是否合适、有无拖沓）
5. 读者体验（代入感、吸引力、可读性）

请提供：
1. 总体评价（优点和不足）
2. 具体问题指出（标注位置）
3. 修改建议（具体可操作）
4. 评分（1-10分，各维度分别评分）

审稿标准：网络小说商业化要求`
  },

  // 优化建议模板
  optimization: {
    name: "内容优化建议",
    category: "optimization",
    content: `请对以下内容提供详细的优化建议：

内容类型：{contentType}（如：章节内容、人物设定、剧情安排等）
内容：
{content}

优化维度：
1. 结构优化
   - 逻辑结构是否合理
   - 层次安排是否清晰
   - 重点是否突出

2. 语言优化
   - 表达是否准确
   - 语言是否流畅
   - 风格是否统一

3. 内容优化
   - 信息是否充实
   - 细节是否到位
   - 深度是否足够

4. 效果优化
   - 是否达到预期目标
   - 读者体验如何
   - 吸引力是否足够

请提供：
1. 具体问题指出（标注位置和问题类型）
2. 优化建议（提供具体可操作的改进方案）
3. 优化示例（给出修改前后的对比）
4. 优先级排序（按重要性排列优化建议）

优化原则：
- 保持原有风格和特色
- 提升整体质量和效果
- 符合目标读者需求
- 便于实际操作执行`
  }
};
```

**写作技巧和风格模板**
```javascript
const writingStyleTemplates = {
  // 写作风格模板
  styleGuide: {
    name: "写作风格指导",
    category: "writing",
    content: `请按照以下风格要求进行写作：

目标风格：{targetStyle}
文本类型：{textType}
字数要求：{wordCount}

风格特点：
{styleCharacteristics}

写作要求：
1. 语言表达要符合{targetStyle}的特点
2. 句式结构要体现风格特色
3. 词汇选择要匹配风格调性
4. 节奏把控要符合风格要求
5. 情感表达要与风格一致

参考示例：
{styleExample}

请严格按照上述风格要求进行创作。`
  },

  // 写作要求模板
  writingRequirements: {
    name: "写作要求规范",
    category: "writing",
    content: `请按照以下写作要求进行创作：

基本要求：
1. 字数控制：{wordCount}字（±10%）
2. 段落结构：每段{paragraphLength}字左右
3. 对话比例：占总字数的{dialogueRatio}%
4. 描写比例：环境描写{environmentRatio}%，心理描写{psychologyRatio}%

内容要求：
1. 情节推进：每{plotInterval}字要有一个情节点
2. 人物塑造：突出{characterTraits}特点
3. 冲突设置：包含{conflictTypes}类型冲突
4. 悬念设置：在{suspensePoints}处设置悬念

语言要求：
1. 语言风格：{languageStyle}
2. 叙述视角：{narrativePerspective}
3. 时态使用：{tenseUsage}
4. 语气把控：{toneControl}

质量标准：
1. 逻辑性：情节发展要合理
2. 连贯性：前后文要呼应
3. 生动性：描写要具体形象
4. 可读性：语言要流畅自然`
  },

  // 仿写模板
  imitation: {
    name: "仿写创作",
    category: "writing",
    content: `请仿照以下范文的风格进行创作：

范文示例：
{referenceText}

仿写要求：
1. 保持相同的写作风格和语言特色
2. 模仿句式结构和表达方式
3. 学习节奏把控和情感表达
4. 借鉴描写手法和叙述技巧

创作内容：
主题：{topic}
字数：{wordCount}字
要求：{requirements}

仿写重点：
- 语言风格的准确模仿
- 句式结构的灵活运用
- 表达技巧的恰当借鉴
- 整体风格的统一协调

请在保持原文风格特色的基础上，创作出符合要求的新内容。`
  },

  // 短篇小说模板
  shortStory: {
    name: "短篇小说创作",
    category: "chapter",
    content: `请创作一篇短篇小说：

基本信息：
标题：{title}
类型：{genre}
字数：{wordCount}字（{minWords}-{maxWords}字）
主题：{theme}

故事要素：
主角：{protagonist}
配角：{supportingCharacters}
背景：{setting}
冲突：{conflict}
结局：{ending}

创作要求：
1. 开头要迅速抓住读者注意力
2. 情节要紧凑，避免拖沓
3. 人物要鲜明，性格要突出
4. 冲突要集中，矛盾要尖锐
5. 结尾要有力，给人深刻印象

结构建议：
- 开头（10%）：快速进入情境
- 发展（30%）：展开情节和人物
- 高潮（40%）：冲突达到顶点
- 结尾（20%）：解决冲突，点题升华

特别注意：
- 短篇要求一气呵成
- 每个字都要有存在价值
- 避免枝节过多
- 主题要鲜明突出`
  }
};
```

**剧情线设计模板**
```javascript
const plotLineTemplates = {
  // 主线剧情设计
  mainPlotline: {
    name: "主线剧情设计",
    category: "plot",
    content: `为小说《{title}》设计主线剧情：

小说信息：
类型：{genre}
主角：{protagonist}
核心冲突：{mainConflict}
目标章节：{totalChapters}章

请设计包含以下要素的主线剧情：
1. 起始事件（引发主线的关键事件）
2. 发展阶段（3-5个主要情节点）
3. 转折点（改变故事走向的关键节点）
4. 高潮部分（矛盾冲突的最高点）
5. 结局安排（主线冲突的解决方案）

剧情线要求：
- 逻辑清晰，前后呼应
- 节奏合理，张弛有度
- 冲突递进，层层深入
- 符合{genre}类型特点
- 为角色成长服务

请按章节分布规划主线剧情的推进节奏。`
  },

  // 副线剧情设计
  subPlotline: {
    name: "副线剧情设计",
    category: "plot",
    content: `为小说设计副线剧情：

主线概况：{mainPlotSummary}
副线类型：{subPlotType}（如：感情线、成长线、复仇线等）
相关角色：{relatedCharacters}

副线剧情设计：
1. 副线起因（为什么需要这条副线）
2. 发展过程（副线的主要情节点）
3. 与主线的交汇点（如何与主线产生关联）
4. 副线结局（如何收尾）
5. 对主线的影响（如何推动或丰富主线）

设计原则：
- 副线要为主线服务
- 不能喧宾夺主
- 要有独立的完整性
- 增加故事的丰富性
- 深化人物关系

请确保副线与主线的有机结合。`
  },

  // 情节点设计
  plotPoints: {
    name: "关键情节点设计",
    category: "plot",
    content: `为第{chapterNumber}章设计关键情节点：

章节背景：
前情回顾：{previousEvents}
本章目标：{chapterGoal}
后续铺垫：{futureSetup}

情节点设计：
1. 开场情节点
   - 场景设置：{openingScene}
   - 人物状态：{characterState}
   - 氛围营造：{atmosphere}

2. 发展情节点（2-3个）
   - 情节点A：{plotPointA}
   - 情节点B：{plotPointB}
   - 情节点C：{plotPointC}

3. 转折情节点
   - 转折事件：{turningEvent}
   - 影响范围：{impact}
   - 后续影响：{consequences}

4. 结尾情节点
   - 章节收尾：{chapterEnding}
   - 悬念设置：{suspense}
   - 下章铺垫：{nextChapterSetup}

要求：
- 每个情节点要有明确目的
- 情节点间要有逻辑关联
- 推动故事向前发展
- 保持读者阅读兴趣`
  }
};
```

#### 5.10.7 技术实现
```typescript
// 提示词模板服务接口
interface PromptTemplateService {
  // 获取模板列表
  getTemplateList(category?: TemplateCategory): Promise<PromptTemplate[]>;

  // 获取单个模板
  getTemplate(templateId: string): Promise<PromptTemplate>;

  // 创建自定义模板
  createTemplate(template: Partial<PromptTemplate>): Promise<string>;

  // 更新模板
  updateTemplate(templateId: string, updates: Partial<PromptTemplate>): Promise<void>;

  // 删除模板
  deleteTemplate(templateId: string): Promise<void>;

  // 渲染模板（变量替换）
  renderTemplate(templateId: string, variables: Record<string, any>): Promise<string>;

  // 导入/导出模板
  exportTemplates(templateIds: string[]): Promise<TemplateExport>;
  importTemplates(templateData: TemplateExport): Promise<string[]>;

  // 模板验证
  validateTemplate(template: PromptTemplate): Promise<ValidationResult>;
}

// 提示词模板数据结构
interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  type: TemplateType;

  // 模板内容
  content: string;
  variables: TemplateVariable[];

  // 模板配置
  config: {
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  };

  // 元数据
  isBuiltIn: boolean;
  isPublic: boolean;
  tags: string[];
  version: string;

  // 使用统计
  usage: {
    useCount: number;
    lastUsed?: Date;
    averageRating?: number;
  };

  // 时间信息
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
}

// 模板分类
enum TemplateCategory {
  OUTLINE = 'outline',           // 大纲生成
  CHAPTER = 'chapter',           // 章节生成
  CHARACTER = 'character',       // 角色生成
  ANALYSIS = 'analysis',         // 内容分析
  OPTIMIZATION = 'optimization', // 内容优化
  DIALOGUE = 'dialogue',         // 对话生成
  DESCRIPTION = 'description',   // 描写生成
  CUSTOM = 'custom'             // 自定义
}

// 模板类型
enum TemplateType {
  SYSTEM = 'system',     // 系统模板
  USER = 'user',         // 用户模板
  ASSISTANT = 'assistant' // 助手模板
}

// 模板变量
interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiline';
  description: string;
  required: boolean;
  defaultValue?: any;
  options?: string[]; // 用于select类型
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    min?: number;
    max?: number;
  };
}

// 模板导出格式
interface TemplateExport {
  version: string;
  exportDate: Date;
  templates: PromptTemplate[];
  metadata: {
    totalCount: number;
    categories: string[];
    exportedBy?: string;
  };
}

// 验证结果
interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

interface ValidationError {
  field: string;
  message: string;
  code: string;
}

interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

// 模板渲染引擎
class TemplateRenderEngine {
  private variablePattern = /\{\{(\w+)\}\}/g;
  private conditionalPattern = /\{\{#if\s+(\w+)\}\}(.*?)\{\{\/if\}\}/gs;
  private loopPattern = /\{\{#each\s+(\w+)\}\}(.*?)\{\{\/each\}\}/gs;

  async renderTemplate(template: PromptTemplate, variables: Record<string, any>): Promise<string> {
    let content = template.content;

    // 验证必需变量
    this.validateRequiredVariables(template, variables);

    // 处理条件语句
    content = this.processConditionals(content, variables);

    // 处理循环语句
    content = this.processLoops(content, variables);

    // 替换变量
    content = this.replaceVariables(content, variables);

    // 后处理
    content = this.postProcess(content);

    return content;
  }

  private validateRequiredVariables(template: PromptTemplate, variables: Record<string, any>) {
    const requiredVars = template.variables.filter(v => v.required);
    const missingVars = requiredVars.filter(v => !(v.name in variables));

    if (missingVars.length > 0) {
      throw new Error(`Missing required variables: ${missingVars.map(v => v.name).join(', ')}`);
    }
  }

  private processConditionals(content: string, variables: Record<string, any>): string {
    return content.replace(this.conditionalPattern, (match, condition, block) => {
      const value = variables[condition];
      return this.isTruthy(value) ? block : '';
    });
  }

  private processLoops(content: string, variables: Record<string, any>): string {
    return content.replace(this.loopPattern, (match, arrayName, block) => {
      const array = variables[arrayName];
      if (!Array.isArray(array)) return '';

      return array.map((item, index) => {
        let itemBlock = block;
        // 替换循环变量
        itemBlock = itemBlock.replace(/\{\{this\}\}/g, String(item));
        itemBlock = itemBlock.replace(/\{\{@index\}\}/g, String(index));
        return itemBlock;
      }).join('');
    });
  }

  private replaceVariables(content: string, variables: Record<string, any>): string {
    return content.replace(this.variablePattern, (match, varName) => {
      const value = variables[varName];
      return value !== undefined ? String(value) : match;
    });
  }

  private postProcess(content: string): string {
    // 清理多余的空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

    // 清理行首行尾空格
    content = content.split('\n').map(line => line.trim()).join('\n');

    // 清理开头和结尾的空行
    content = content.trim();

    return content;
  }

  private isTruthy(value: any): boolean {
    if (value === null || value === undefined) return false;
    if (typeof value === 'boolean') return value;
    if (typeof value === 'number') return value !== 0;
    if (typeof value === 'string') return value.length > 0;
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object') return Object.keys(value).length > 0;
    return Boolean(value);
  }
}

// 模板管理器
class TemplateManager {
  private templates = new Map<string, PromptTemplate>();
  private renderEngine = new TemplateRenderEngine();

  async loadBuiltInTemplates() {
    const builtInTemplates = await this.getBuiltInTemplates();
    builtInTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  async createTemplate(templateData: Partial<PromptTemplate>): Promise<string> {
    const template: PromptTemplate = {
      id: this.generateId(),
      name: templateData.name || '未命名模板',
      description: templateData.description || '',
      category: templateData.category || TemplateCategory.CUSTOM,
      type: templateData.type || TemplateType.USER,
      content: templateData.content || '',
      variables: templateData.variables || [],
      config: templateData.config || {},
      isBuiltIn: false,
      isPublic: templateData.isPublic || false,
      tags: templateData.tags || [],
      version: '1.0.0',
      usage: {
        useCount: 0
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: templateData.createdBy
    };

    // 验证模板
    const validation = await this.validateTemplate(template);
    if (!validation.isValid) {
      throw new Error(`Template validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
    }

    this.templates.set(template.id, template);
    await this.saveTemplate(template);

    return template.id;
  }

  async renderTemplate(templateId: string, variables: Record<string, any>): Promise<string> {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    // 更新使用统计
    template.usage.useCount++;
    template.usage.lastUsed = new Date();

    return this.renderEngine.renderTemplate(template, variables);
  }

  async validateTemplate(template: PromptTemplate): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 基本验证
    if (!template.name.trim()) {
      errors.push({
        field: 'name',
        message: '模板名称不能为空',
        code: 'REQUIRED_FIELD'
      });
    }

    if (!template.content.trim()) {
      errors.push({
        field: 'content',
        message: '模板内容不能为空',
        code: 'REQUIRED_FIELD'
      });
    }

    // 变量验证
    const contentVariables = this.extractVariablesFromContent(template.content);
    const definedVariables = template.variables.map(v => v.name);

    // 检查未定义的变量
    const undefinedVars = contentVariables.filter(v => !definedVariables.includes(v));
    if (undefinedVars.length > 0) {
      warnings.push({
        field: 'variables',
        message: `发现未定义的变量: ${undefinedVars.join(', ')}`,
        suggestion: '请在变量列表中定义这些变量'
      });
    }

    // 检查未使用的变量
    const unusedVars = definedVariables.filter(v => !contentVariables.includes(v));
    if (unusedVars.length > 0) {
      warnings.push({
        field: 'variables',
        message: `发现未使用的变量: ${unusedVars.join(', ')}`,
        suggestion: '考虑删除这些未使用的变量定义'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  private extractVariablesFromContent(content: string): string[] {
    const variables = new Set<string>();
    const matches = content.matchAll(/\{\{(\w+)\}\}/g);

    for (const match of matches) {
      variables.add(match[1]);
    }

    return Array.from(variables);
  }

  private async getBuiltInTemplates(): Promise<PromptTemplate[]> {
    // 返回内置模板列表
    // 实际实现中从配置文件或数据库加载
    return [];
  }

  private async saveTemplate(template: PromptTemplate): Promise<void> {
    // 保存模板到数据库
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

// 模板导入导出管理
class TemplateImportExportManager {
  async exportTemplates(templateIds: string[]): Promise<TemplateExport> {
    const templates = await Promise.all(
      templateIds.map(id => this.templateService.getTemplate(id))
    );

    return {
      version: '1.0.0',
      exportDate: new Date(),
      templates,
      metadata: {
        totalCount: templates.length,
        categories: [...new Set(templates.map(t => t.category))],
        exportedBy: 'AI小说助手'
      }
    };
  }

  async importTemplates(templateData: TemplateExport): Promise<string[]> {
    const importedIds: string[] = [];

    for (const template of templateData.templates) {
      try {
        // 重新生成ID避免冲突
        const newTemplate = {
          ...template,
          id: this.generateId(),
          isBuiltIn: false,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const id = await this.templateService.createTemplate(newTemplate);
        importedIds.push(id);
      } catch (error) {
        console.error(`Failed to import template ${template.name}:`, error);
      }
    }

    return importedIds;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
```

### 5.11 降AI味功能模块

#### 5.11.1 功能概述
降AI味功能模块专门用于优化AI生成的小说内容，减少机械化表达，让AI生成的内容更加自然、生动，更符合人类写作风格。

#### 5.11.2 核心功能
- **AI味检测**: 自动识别AI生成内容中的机械化表达
- **表达优化**: 将僵硬的表达转换为自然流畅的语言
- **风格调整**: 根据小说类型调整语言风格
- **情感增强**: 增强内容的情感表达和感染力
- **个性化处理**: 保持角色语言的个性化特征
- **批量处理**: 支持对整章或多章内容进行批量优化

#### 5.11.3 AI味识别规则

**常见AI味特征**
```typescript
interface AIFlavorDetection {
  // 机械化表达模式
  mechanicalPatterns: {
    repetitiveStructures: string[];    // 重复结构
    formulaicPhrases: string[];        // 公式化短语
    overusedTransitions: string[];     // 过度使用的转折词
    artificialDialogue: string[];      // 不自然的对话
  };

  // 情感表达问题
  emotionalIssues: {
    flatEmotions: string[];           // 平淡的情感描述
    genericReactions: string[];       // 通用化反应
    lackOfNuance: string[];          // 缺乏细腻度
  };

  // 语言风格问题
  styleIssues: {
    overFormalLanguage: string[];     // 过于正式的语言
    inconsistentTone: string[];       // 语调不一致
    lackOfPersonality: string[];      // 缺乏个性化
  };
}

const aiFlavorRules = {
  // 检测重复模式
  repetitivePatterns: [
    /他\/她(的眼中|心中|脸上)闪过一丝/g,
    /不禁(想起|想到|回忆起)/g,
    /心中(暗想|暗道|想道)/g,
    /眼中(闪过|流露出|透露出)/g
  ],

  // 检测公式化表达
  formulaicExpressions: [
    /显然|毫无疑问|不言而喻/g,
    /与此同时|就在这时|正在此时/g,
    /总而言之|综上所述|由此可见/g
  ],

  // 检测情感平淡
  flatEmotions: [
    /感到(高兴|难过|愤怒|惊讶)/g,
    /心情(很好|不好|复杂)/g,
    /觉得(有趣|无聊|奇怪)/g
  ]
};
```

#### 5.11.4 优化策略设计

**表达优化算法**
```typescript
class AIFlavorOptimizer {
  // 主要优化方法
  async optimizeContent(content: string, options: OptimizationOptions): Promise<string> {
    let optimizedContent = content;

    // 1. 检测AI味特征
    const aiFeatures = this.detectAIFlavor(content);

    // 2. 应用优化策略
    optimizedContent = await this.applyOptimizations(optimizedContent, aiFeatures);

    // 3. 风格调整
    optimizedContent = await this.adjustStyle(optimizedContent, options.targetStyle);

    // 4. 情感增强
    optimizedContent = await this.enhanceEmotions(optimizedContent);

    // 5. 个性化处理
    optimizedContent = await this.personalizeCharacters(optimizedContent, options.characters);

    return optimizedContent;
  }

  // 表达多样化
  private diversifyExpressions(content: string): string {
    const expressions = {
      '他想': ['他琢磨着', '他寻思', '他暗忖', '他心里嘀咕'],
      '她说': ['她开口道', '她轻声说', '她缓缓道', '她低语'],
      '很高兴': ['欣喜若狂', '心花怒放', '喜不自胜', '乐得合不拢嘴'],
      '很生气': ['怒火中烧', '气得七窍生烟', '勃然大怒', '怒不可遏']
    };

    let result = content;
    for (const [pattern, alternatives] of Object.entries(expressions)) {
      const regex = new RegExp(pattern, 'g');
      result = result.replace(regex, () => {
        return alternatives[Math.floor(Math.random() * alternatives.length)];
      });
    }

    return result;
  }

  // 情感细腻化
  private refineEmotions(content: string): string {
    const emotionMappings = {
      '高兴': {
        mild: '心情不错',
        moderate: '喜悦',
        intense: '狂欢不已'
      },
      '难过': {
        mild: '有些失落',
        moderate: '心情沉重',
        intense: '痛不欲生'
      }
    };

    // 根据上下文判断情感强度并替换
    return this.contextualEmotionReplacement(content, emotionMappings);
  }
}
```

#### 5.11.5 技术实现
```typescript
// 降AI味服务接口
interface DeAIService {
  // 检测AI味
  detectAIFlavor(content: string): Promise<AIFlavorAnalysis>;

  // 优化内容
  optimizeContent(content: string, options: OptimizationOptions): Promise<OptimizedContent>;

  // 批量优化
  batchOptimize(contents: string[], options: OptimizationOptions): Promise<OptimizedContent[]>;

  // 获取优化建议
  getOptimizationSuggestions(content: string): Promise<OptimizationSuggestion[]>;

  // 应用特定优化
  applySpecificOptimization(content: string, optimization: OptimizationType): Promise<string>;

  // 自定义规则管理
  addCustomRule(rule: CustomOptimizationRule): Promise<string>;
  updateCustomRule(ruleId: string, updates: Partial<CustomOptimizationRule>): Promise<void>;
  deleteCustomRule(ruleId: string): Promise<void>;
}

// AI味分析结果
interface AIFlavorAnalysis {
  overallScore: number; // 0-100，越高AI味越重
  confidence: number;   // 检测置信度

  // 分类检测结果
  categories: {
    repetitiveExpressions: DetectionResult;    // 重复表达
    mechanicalTransitions: DetectionResult;   // 机械化转折
    artificialDialogue: DetectionResult;      // 不自然对话
    genericDescriptions: DetectionResult;     // 通用化描述
    overusedPhrases: DetectionResult;        // 过度使用的短语
    lackOfPersonality: DetectionResult;      // 缺乏个性
  };

  // 具体问题点
  issues: AIFlavorIssue[];

  // 改进建议
  suggestions: string[];

  analysisDate: Date;
  modelVersion: string;
}

interface DetectionResult {
  score: number;        // 该类别的AI味评分
  confidence: number;   // 检测置信度
  examples: string[];   // 具体示例
  count: number;        // 发现的问题数量
}

interface AIFlavorIssue {
  type: AIFlavorType;
  severity: 'low' | 'medium' | 'high';
  position: {
    start: number;
    end: number;
    line?: number;
    column?: number;
  };
  originalText: string;
  description: string;
  suggestions: string[];
  autoFixAvailable: boolean;
}

enum AIFlavorType {
  REPETITIVE_EXPRESSION = 'repetitive_expression',
  MECHANICAL_TRANSITION = 'mechanical_transition',
  ARTIFICIAL_DIALOGUE = 'artificial_dialogue',
  GENERIC_DESCRIPTION = 'generic_description',
  OVERUSED_PHRASE = 'overused_phrase',
  LACK_OF_PERSONALITY = 'lack_of_personality',
  UNNATURAL_FLOW = 'unnatural_flow',
  TEMPLATE_LANGUAGE = 'template_language'
}

// 优化选项
interface OptimizationOptions {
  targetStyle: 'natural' | 'literary' | 'conversational' | 'dramatic';
  aggressiveness: 'conservative' | 'moderate' | 'aggressive';
  preserveLength: boolean;
  preserveMeaning: boolean;
  customRules: string[];
  excludeTypes: AIFlavorType[];
}

// 优化结果
interface OptimizedContent {
  originalContent: string;
  optimizedContent: string;

  // 优化统计
  statistics: {
    totalChanges: number;
    improvementScore: number; // 改进程度评分
    processingTime: number;   // 处理时间（毫秒）
  };

  // 变更详情
  changes: ContentChange[];

  // 质量评估
  qualityAssessment: {
    readabilityScore: number;
    naturalness: number;
    coherence: number;
    engagement: number;
  };
}

interface ContentChange {
  type: AIFlavorType;
  position: { start: number; end: number };
  originalText: string;
  optimizedText: string;
  reason: string;
  confidence: number;
}

// 优化建议
interface OptimizationSuggestion {
  type: OptimizationType;
  priority: 'high' | 'medium' | 'low';
  description: string;
  example: {
    before: string;
    after: string;
  };
  applicableCount: number; // 可应用的位置数量
}

enum OptimizationType {
  VARY_SENTENCE_STRUCTURE = 'vary_sentence_structure',
  ENHANCE_DIALOGUE = 'enhance_dialogue',
  IMPROVE_TRANSITIONS = 'improve_transitions',
  ADD_SENSORY_DETAILS = 'add_sensory_details',
  PERSONALIZE_DESCRIPTIONS = 'personalize_descriptions',
  REDUCE_REPETITION = 'reduce_repetition',
  STRENGTHEN_VOICE = 'strengthen_voice'
}

// 自定义优化规则
interface CustomOptimizationRule {
  id: string;
  name: string;
  description: string;

  // 匹配规则
  pattern: {
    type: 'regex' | 'phrase' | 'semantic';
    value: string;
    flags?: string;
  };

  // 替换规则
  replacement: {
    type: 'static' | 'template' | 'ai_generated';
    value: string;
    variables?: string[];
  };

  // 应用条件
  conditions: {
    minLength?: number;
    maxLength?: number;
    context?: string[];
    excludeContext?: string[];
  };

  // 规则配置
  enabled: boolean;
  priority: number;
  category: AIFlavorType;

  createdAt: Date;
  updatedAt: Date;
}

// AI味检测引擎
class AIFlavorDetector {
  private patterns: Map<AIFlavorType, RegExp[]> = new Map();
  private phraseDatabase: Map<string, number> = new Map(); // 短语 -> AI味权重

  constructor() {
    this.initializePatterns();
    this.loadPhraseDatabase();
  }

  async detectAIFlavor(content: string): Promise<AIFlavorAnalysis> {
    const issues: AIFlavorIssue[] = [];
    const categoryResults: Record<string, DetectionResult> = {};

    // 检测各类AI味问题
    for (const [type, patterns] of this.patterns) {
      const result = this.detectCategory(content, type, patterns);
      categoryResults[type] = result;
      issues.push(...result.issues);
    }

    // 计算总体评分
    const overallScore = this.calculateOverallScore(categoryResults);
    const confidence = this.calculateConfidence(categoryResults);

    // 生成改进建议
    const suggestions = this.generateSuggestions(issues);

    return {
      overallScore,
      confidence,
      categories: categoryResults as any,
      issues,
      suggestions,
      analysisDate: new Date(),
      modelVersion: '1.0.0'
    };
  }

  private detectCategory(content: string, type: AIFlavorType, patterns: RegExp[]): DetectionResult {
    const examples: string[] = [];
    const issues: AIFlavorIssue[] = [];
    let totalMatches = 0;

    for (const pattern of patterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        if (match.index !== undefined) {
          const text = match[0];
          examples.push(text);
          totalMatches++;

          issues.push({
            type,
            severity: this.calculateSeverity(type, text),
            position: {
              start: match.index,
              end: match.index + text.length
            },
            originalText: text,
            description: this.getIssueDescription(type, text),
            suggestions: this.getIssueSuggestions(type, text),
            autoFixAvailable: this.hasAutoFix(type)
          });
        }
      }
    }

    // 计算该类别的评分
    const score = Math.min(100, totalMatches * 10);
    const confidence = Math.min(1, totalMatches / 5);

    return {
      score,
      confidence,
      examples: examples.slice(0, 5), // 最多返回5个示例
      count: totalMatches
    };
  }

  private initializePatterns() {
    // 重复表达模式
    this.patterns.set(AIFlavorType.REPETITIVE_EXPRESSION, [
      /他的心中.*?他的心中/g,
      /突然.*?突然/g,
      /不禁.*?不禁/g,
      /仿佛.*?仿佛/g
    ]);

    // 机械化转折模式
    this.patterns.set(AIFlavorType.MECHANICAL_TRANSITION, [
      /然而，就在这时/g,
      /正当.*?的时候/g,
      /就在此时此刻/g,
      /与此同时/g
    ]);

    // 不自然对话模式
    this.patterns.set(AIFlavorType.ARTIFICIAL_DIALOGUE, [
      /".*?，我.*?。"/g,
      /"不，这不可能！"/g,
      /"你说得对"/g
    ]);

    // 通用化描述模式
    this.patterns.set(AIFlavorType.GENERIC_DESCRIPTION, [
      /阳光透过.*?洒在/g,
      /微风轻抚着/g,
      /时间仿佛静止了/g
    ]);

    // 过度使用的短语
    this.patterns.set(AIFlavorType.OVERUSED_PHRASE, [
      /心中五味杂陈/g,
      /百感交集/g,
      /思绪万千/g,
      /心潮澎湃/g
    ]);
  }

  private loadPhraseDatabase() {
    // 加载AI味短语数据库
    const aiPhrases = [
      { phrase: '心中五味杂陈', weight: 0.8 },
      { phrase: '思绪万千', weight: 0.7 },
      { phrase: '百感交集', weight: 0.9 },
      { phrase: '心潮澎湃', weight: 0.6 }
    ];

    aiPhrases.forEach(({ phrase, weight }) => {
      this.phraseDatabase.set(phrase, weight);
    });
  }

  private calculateOverallScore(categoryResults: Record<string, DetectionResult>): number {
    const scores = Object.values(categoryResults).map(r => r.score);
    const weights = [0.2, 0.2, 0.15, 0.15, 0.15, 0.15]; // 各类别权重

    let weightedSum = 0;
    let totalWeight = 0;

    scores.forEach((score, index) => {
      const weight = weights[index] || 0.1;
      weightedSum += score * weight;
      totalWeight += weight;
    });

    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }

  private calculateConfidence(categoryResults: Record<string, DetectionResult>): number {
    const confidences = Object.values(categoryResults).map(r => r.confidence);
    return confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
  }

  private calculateSeverity(type: AIFlavorType, text: string): 'low' | 'medium' | 'high' {
    const weight = this.phraseDatabase.get(text) || 0.5;
    if (weight > 0.8) return 'high';
    if (weight > 0.5) return 'medium';
    return 'low';
  }

  private getIssueDescription(type: AIFlavorType, text: string): string {
    const descriptions = {
      [AIFlavorType.REPETITIVE_EXPRESSION]: '重复使用相同的表达方式',
      [AIFlavorType.MECHANICAL_TRANSITION]: '使用机械化的转折词',
      [AIFlavorType.ARTIFICIAL_DIALOGUE]: '对话显得不够自然',
      [AIFlavorType.GENERIC_DESCRIPTION]: '使用过于通用的描述',
      [AIFlavorType.OVERUSED_PHRASE]: '过度使用常见短语',
      [AIFlavorType.LACK_OF_PERSONALITY]: '缺乏个性化表达'
    };

    return descriptions[type] || '发现AI味问题';
  }

  private getIssueSuggestions(type: AIFlavorType, text: string): string[] {
    // 根据问题类型和具体文本生成建议
    const suggestions = {
      [AIFlavorType.REPETITIVE_EXPRESSION]: [
        '尝试使用不同的表达方式',
        '增加句式变化',
        '使用同义词替换'
      ],
      [AIFlavorType.MECHANICAL_TRANSITION]: [
        '使用更自然的过渡',
        '通过情节推进代替转折词',
        '让转折更符合情境'
      ]
    };

    return suggestions[type] || ['考虑重新表达这部分内容'];
  }

  private hasAutoFix(type: AIFlavorType): boolean {
    const autoFixTypes = [
      AIFlavorType.OVERUSED_PHRASE,
      AIFlavorType.REPETITIVE_EXPRESSION
    ];

    return autoFixTypes.includes(type);
  }

  private generateSuggestions(issues: AIFlavorIssue[]): string[] {
    const suggestions: string[] = [];

    if (issues.length > 10) {
      suggestions.push('内容中AI味较重，建议进行全面优化');
    }

    const highSeverityIssues = issues.filter(i => i.severity === 'high');
    if (highSeverityIssues.length > 0) {
      suggestions.push('优先处理高严重度的AI味问题');
    }

    const typeGroups = this.groupIssuesByType(issues);
    for (const [type, typeIssues] of typeGroups) {
      if (typeIssues.length > 3) {
        suggestions.push(`${type}类问题较多，建议重点关注`);
      }
    }

    return suggestions;
  }

  private groupIssuesByType(issues: AIFlavorIssue[]): Map<AIFlavorType, AIFlavorIssue[]> {
    const groups = new Map<AIFlavorType, AIFlavorIssue[]>();

    issues.forEach(issue => {
      if (!groups.has(issue.type)) {
        groups.set(issue.type, []);
      }
      groups.get(issue.type)!.push(issue);
    });

    return groups;
  }
}

// 内容优化引擎
class ContentOptimizer {
  private detector = new AIFlavorDetector();

  async optimizeContent(content: string, options: OptimizationOptions): Promise<OptimizedContent> {
    const startTime = Date.now();

    // 首先检测AI味
    const analysis = await this.detector.detectAIFlavor(content);

    // 根据选项过滤需要处理的问题
    const targetIssues = analysis.issues.filter(issue =>
      !options.excludeTypes.includes(issue.type)
    );

    // 应用优化
    let optimizedContent = content;
    const changes: ContentChange[] = [];

    for (const issue of targetIssues) {
      if (this.shouldOptimize(issue, options)) {
        const optimized = await this.optimizeIssue(optimizedContent, issue, options);
        if (optimized.changed) {
          optimizedContent = optimized.content;
          changes.push({
            type: issue.type,
            position: issue.position,
            originalText: issue.originalText,
            optimizedText: optimized.replacement,
            reason: optimized.reason,
            confidence: optimized.confidence
          });
        }
      }
    }

    // 计算统计信息
    const processingTime = Date.now() - startTime;
    const improvementScore = this.calculateImprovementScore(analysis, changes);

    // 质量评估
    const qualityAssessment = await this.assessQuality(optimizedContent);

    return {
      originalContent: content,
      optimizedContent,
      statistics: {
        totalChanges: changes.length,
        improvementScore,
        processingTime
      },
      changes,
      qualityAssessment
    };
  }

  private shouldOptimize(issue: AIFlavorIssue, options: OptimizationOptions): boolean {
    // 根据严重程度和激进程度决定是否优化
    if (options.aggressiveness === 'conservative' && issue.severity === 'low') {
      return false;
    }

    if (options.aggressiveness === 'moderate' && issue.severity === 'low' && Math.random() > 0.5) {
      return false;
    }

    return true;
  }

  private async optimizeIssue(content: string, issue: AIFlavorIssue, options: OptimizationOptions): Promise<{
    changed: boolean;
    content: string;
    replacement: string;
    reason: string;
    confidence: number;
  }> {
    // 根据问题类型应用不同的优化策略
    switch (issue.type) {
      case AIFlavorType.OVERUSED_PHRASE:
        return this.optimizeOverusedPhrase(content, issue, options);
      case AIFlavorType.REPETITIVE_EXPRESSION:
        return this.optimizeRepetitiveExpression(content, issue, options);
      case AIFlavorType.MECHANICAL_TRANSITION:
        return this.optimizeMechanicalTransition(content, issue, options);
      default:
        return { changed: false, content, replacement: '', reason: '', confidence: 0 };
    }
  }

  private async optimizeOverusedPhrase(content: string, issue: AIFlavorIssue, options: OptimizationOptions): Promise<any> {
    // 过度使用短语的优化逻辑
    const alternatives = this.getAlternatives(issue.originalText);
    if (alternatives.length > 0) {
      const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
      const newContent = content.substring(0, issue.position.start) +
                        replacement +
                        content.substring(issue.position.end);

      return {
        changed: true,
        content: newContent,
        replacement,
        reason: '替换过度使用的短语',
        confidence: 0.8
      };
    }

    return { changed: false, content, replacement: '', reason: '', confidence: 0 };
  }

  private async optimizeRepetitiveExpression(content: string, issue: AIFlavorIssue, options: OptimizationOptions): Promise<any> {
    // 重复表达的优化逻辑
    // 实现具体的优化算法
    return { changed: false, content, replacement: '', reason: '', confidence: 0 };
  }

  private async optimizeMechanicalTransition(content: string, issue: AIFlavorIssue, options: OptimizationOptions): Promise<any> {
    // 机械化转折的优化逻辑
    // 实现具体的优化算法
    return { changed: false, content, replacement: '', reason: '', confidence: 0 };
  }

  private getAlternatives(phrase: string): string[] {
    // 获取短语的替代表达
    const alternatives: Record<string, string[]> = {
      '心中五味杂陈': ['内心复杂', '心情复杂', '百感交集', '心绪难平'],
      '思绪万千': ['思绪纷乱', '心思复杂', '想法很多', '心事重重'],
      '百感交集': ['心情复杂', '感慨万千', '心绪难平', '五味杂陈']
    };

    return alternatives[phrase] || [];
  }

  private calculateImprovementScore(analysis: AIFlavorAnalysis, changes: ContentChange[]): number {
    // 计算改进程度评分
    const originalScore = analysis.overallScore;
    const changeImpact = changes.reduce((sum, change) => sum + change.confidence, 0);

    return Math.min(100, changeImpact * 10);
  }

  private async assessQuality(content: string): Promise<any> {
    // 评估优化后内容的质量
    return {
      readabilityScore: 85,
      naturalness: 80,
      coherence: 90,
      engagement: 75
    };
  }
}
```

### 5.12 记忆窗口功能模块

#### 5.12.1 功能概述
记忆窗口功能模块负责记住用户对应用窗口的个性化设置，包括窗口大小、位置、布局等，提升用户体验的连续性。

#### 5.12.2 核心功能
- **窗口状态记忆**: 记住窗口的大小、位置、最大化状态
- **布局记忆**: 记住侧边栏宽度、面板分割比例等
- **主题记忆**: 记住用户选择的主题和界面设置
- **工作区记忆**: 记住最后打开的项目和工作状态
- **自动恢复**: 应用启动时自动恢复上次的窗口状态
- **多显示器支持**: 支持多显示器环境下的窗口状态记忆

#### 5.12.3 技术实现设计

**窗口状态数据结构**
```typescript
interface WindowState {
  // 窗口基本信息
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };

  // 窗口状态
  isMaximized: boolean;
  isMinimized: boolean;
  isFullScreen: boolean;

  // 显示器信息
  display: {
    id: number;
    bounds: Rectangle;
    workArea: Rectangle;
  };

  // 布局信息
  layout: {
    sidebarWidth: number;
    panelSizes: number[];
    activeTab: string;
    openPanels: string[];
  };

  // 主题设置
  theme: {
    mode: 'light' | 'dark' | 'auto';
    primaryColor: string;
    fontSize: number;
  };

  // 工作区状态
  workspace: {
    lastOpenProject: string;
    recentFiles: string[];
    openTabs: string[];
    activeModule: string;
  };
}

class WindowStateManager {
  private configPath: string;
  private state: WindowState;

  // 保存窗口状态
  async saveWindowState(window: BrowserWindow): Promise<void> {
    const bounds = window.getBounds();
    const isMaximized = window.isMaximized();
    const display = screen.getDisplayMatching(bounds);

    this.state = {
      bounds,
      isMaximized,
      isMinimized: window.isMinimized(),
      isFullScreen: window.isFullScreen(),
      display: {
        id: display.id,
        bounds: display.bounds,
        workArea: display.workArea
      },
      layout: await this.getLayoutState(),
      theme: await this.getThemeState(),
      workspace: await this.getWorkspaceState()
    };

    await this.persistState();
  }

  // 恢复窗口状态
  async restoreWindowState(window: BrowserWindow): Promise<void> {
    await this.loadState();

    if (this.state && this.isValidState(this.state)) {
      // 检查显示器是否仍然存在
      const displays = screen.getAllDisplays();
      const targetDisplay = displays.find(d => d.id === this.state.display.id);

      if (targetDisplay) {
        // 恢复到原显示器
        window.setBounds(this.state.bounds);
      } else {
        // 显示器不存在，恢复到主显示器
        const primaryDisplay = screen.getPrimaryDisplay();
        const centeredBounds = this.centerOnDisplay(this.state.bounds, primaryDisplay);
        window.setBounds(centeredBounds);
      }

      if (this.state.isMaximized) {
        window.maximize();
      }

      // 恢复布局和主题
      await this.restoreLayout();
      await this.restoreTheme();
      await this.restoreWorkspace();
    }
  }

  // 验证状态有效性
  private isValidState(state: WindowState): boolean {
    const { bounds } = state;
    return bounds.width > 0 && bounds.height > 0 &&
           bounds.width <= 4096 && bounds.height <= 4096;
  }
}
```




### 5.13 设置模块

#### 5.13.1 功能概述
设置模块是应用的配置中心，管理所有AI服务的API配置和应用偏好设置。

#### 5.13.2 核心功能
- **API配置管理**: 支持多种AI服务商的API配置
- **智能API检测**: 自动检测和纠正API地址
- **模型管理**: 自定义模型的添加、编辑、删除
- **连接测试**: 验证API配置的有效性
- **安全存储**: API密钥的加密存储
- **配置导入导出**: 配置的备份和恢复
- **统一API管理**: 集中管理所有AI服务配置，支持快速切换

#### 5.13.3 统一API保存管理功能

**API配置统一管理**
```javascript
const apiConfigManager = {
  // API配置数据结构
  apiConfigs: {
    openai: {
      name: "OpenAI",
      enabled: true,
      configs: [
        {
          id: "openai-1",
          name: "主要配置",
          apiKey: "sk-xxx...",
          model: "gpt-4-turbo",
          apiUrl: "https://api.openai.com/v1/chat/completions",
          status: "connected",
          lastTested: "2024-01-29 14:30",
          isDefault: true
        }
      ]
    },
    anthropic: {
      name: "Anthropic",
      enabled: true,
      configs: [
        {
          id: "claude-1",
          name: "Claude配置",
          apiKey: "sk-ant-xxx...",
          model: "claude-3-opus-20240229",
          apiUrl: "https://api.anthropic.com/v1/messages",
          status: "connected",
          lastTested: "2024-01-29 14:25",
          isDefault: false
        }
      ]
    },
    custom: {
      name: "自定义模型",
      enabled: true,
      configs: [
        {
          id: "custom-1",
          name: "DeepSeek配置",
          apiKey: "sk-xxx...",
          model: "deepseek-chat",
          apiUrl: "https://api.deepseek.com/v1/chat/completions",
          status: "untested",
          lastTested: null,
          isDefault: false
        }
      ]
    }
  },

  // 保存API配置
  saveApiConfig: (provider, config) => {
    // 验证配置完整性
    if (!validateApiConfig(config)) {
      throw new Error("API配置不完整");
    }

    // 加密存储API密钥
    const encryptedConfig = {
      ...config,
      apiKey: encrypt(config.apiKey)
    };

    // 保存到配置文件
    const configs = apiConfigManager.apiConfigs[provider].configs;
    const existingIndex = configs.findIndex(c => c.id === config.id);

    if (existingIndex >= 0) {
      configs[existingIndex] = encryptedConfig;
    } else {
      configs.push(encryptedConfig);
    }

    // 持久化存储
    saveConfigToFile(apiConfigManager.apiConfigs);

    return { success: true, message: "API配置保存成功" };
  },

  // 获取可用的API配置列表
  getAvailableConfigs: () => {
    const availableConfigs = [];

    Object.entries(apiConfigManager.apiConfigs).forEach(([provider, data]) => {
      if (data.enabled) {
        data.configs.forEach(config => {
          if (config.status === 'connected') {
            availableConfigs.push({
              id: config.id,
              provider: provider,
              name: `${data.name} - ${config.name}`,
              model: config.model,
              isDefault: config.isDefault
            });
          }
        });
      }
    });

    return availableConfigs.sort((a, b) => b.isDefault - a.isDefault);
  },

  // 快速切换API配置
  switchApiConfig: (configId) => {
    const config = findConfigById(configId);
    if (!config) {
      throw new Error("配置不存在");
    }

    if (config.status !== 'connected') {
      throw new Error("配置未连接，请先测试连接");
    }

    // 设置为当前使用的配置
    setCurrentApiConfig(config);

    return { success: true, config: config };
  },

  // 批量测试API连接
  batchTestConnections: async () => {
    const results = [];

    for (const [provider, data] of Object.entries(apiConfigManager.apiConfigs)) {
      if (data.enabled) {
        for (const config of data.configs) {
          try {
            const testResult = await testApiConnection(config);
            config.status = testResult.success ? 'connected' : 'failed';
            config.lastTested = new Date().toISOString();

            results.push({
              id: config.id,
              name: `${data.name} - ${config.name}`,
              status: config.status,
              message: testResult.message
            });
          } catch (error) {
            config.status = 'error';
            results.push({
              id: config.id,
              name: `${data.name} - ${config.name}`,
              status: 'error',
              message: error.message
            });
          }
        }
      }
    }

    // 保存更新后的状态
    saveConfigToFile(apiConfigManager.apiConfigs);

    return results;
  }
};
```

#### 5.13.4 智能API地址检测功能
```javascript
const apiAddressDetection = {
  // 检测API地址有效性
  validateApiAddress: async (url, apiKey) => {
    try {
      // 1. 检查URL格式
      if (!isValidUrl(url)) {
        return { valid: false, error: "无效的URL格式" };
      }

      // 2. 尝试连接测试
      const response = await testConnection(url, apiKey);
      if (response.success) {
        return { valid: true, correctedUrl: url };
      }

      // 3. 如果失败，尝试自动纠正
      const correctedUrl = await autoCorrectApiUrl(url);
      if (correctedUrl) {
        const retryResponse = await testConnection(correctedUrl, apiKey);
        if (retryResponse.success) {
          return {
            valid: true,
            correctedUrl: correctedUrl,
            message: "已自动纠正API地址"
          };
        }
      }

      return { valid: false, error: "无法连接到API服务" };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  },

  // 自动纠正API地址
  autoCorrectApiUrl: (url) => {
    const corrections = [
      // OpenAI API纠正
      {
        pattern: /api\.openai\.com(?!\/v1)/,
        correction: "api.openai.com/v1/chat/completions"
      },
      // Anthropic API纠正
      {
        pattern: /api\.anthropic\.com(?!\/v1)/,
        correction: "api.anthropic.com/v1/messages"
      },
      // 其他常见纠正...
    ];

    for (const rule of corrections) {
      if (rule.pattern.test(url)) {
        return url.replace(rule.pattern, rule.correction);
      }
    }

    return null;
  }
};
```

#### 5.13.5 技术实现
```typescript
// 设置服务接口
interface SettingsService {
  // 获取所有设置
  getAllSettings(): Promise<ApplicationSettings>;

  // 获取特定设置
  getSetting<T>(key: string): Promise<T>;

  // 更新设置
  updateSetting<T>(key: string, value: T): Promise<void>;

  // 批量更新设置
  updateSettings(settings: Partial<ApplicationSettings>): Promise<void>;

  // 重置设置
  resetSettings(keys?: string[]): Promise<void>;

  // 导入/导出设置
  exportSettings(): Promise<SettingsExport>;
  importSettings(settingsData: SettingsExport): Promise<void>;

  // 设置验证
  validateSettings(settings: Partial<ApplicationSettings>): Promise<ValidationResult>;

  // API配置管理
  addAPIConfig(config: APIConfig): Promise<string>;
  updateAPIConfig(configId: string, updates: Partial<APIConfig>): Promise<void>;
  deleteAPIConfig(configId: string): Promise<void>;
  testAPIConfig(configId: string): Promise<APITestResult>;
}

// 应用设置数据结构
interface ApplicationSettings {
  // API配置
  apiConfigs: APIConfig[];
  defaultAPIConfig: string;

  // 应用偏好
  preferences: {
    // 界面设置
    ui: {
      theme: 'light' | 'dark' | 'auto';
      language: 'zh-CN' | 'en-US';
      fontSize: number;
      fontFamily: string;
      compactMode: boolean;
      showLineNumbers: boolean;
      wordWrap: boolean;
    };

    // 编辑器设置
    editor: {
      autoSave: boolean;
      autoSaveInterval: number; // 秒
      tabSize: number;
      insertSpaces: boolean;
      showWhitespace: boolean;
      highlightCurrentLine: boolean;
      enableCodeFolding: boolean;
    };

    // AI生成设置
    aiGeneration: {
      defaultModel: string;
      defaultTemperature: number;
      defaultMaxTokens: number;
      enableContextOptimization: boolean;
      autoDetectLanguage: boolean;
      enableAIFlavorReduction: boolean;
    };

    // 项目设置
    project: {
      defaultProjectPath: string;
      autoBackup: boolean;
      backupInterval: number; // 分钟
      maxBackupFiles: number;
      enableVersionControl: boolean;
    };

    // 性能设置
    performance: {
      enableHardwareAcceleration: boolean;
      maxMemoryUsage: number; // MB
      enableLazyLoading: boolean;
      cacheSize: number; // MB
    };

    // 隐私设置
    privacy: {
      enableTelemetry: boolean;
      enableCrashReporting: boolean;
      enableUsageAnalytics: boolean;
      dataRetentionDays: number;
    };
  };

  // 窗口状态
  windowState: WindowState;

  // 快捷键配置
  shortcuts: KeyboardShortcuts;

  // 插件设置
  plugins: PluginSettings;

  // 元数据
  metadata: {
    version: string;
    lastUpdated: Date;
    migrationVersion: number;
  };
}

// API配置
interface APIConfig {
  id: string;
  name: string;
  provider: AIProvider;

  // 连接配置
  connection: {
    apiKey: string;
    apiUrl: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
  };

  // 模型配置
  models: {
    textGeneration: string[];
    embedding: string[];
    analysis: string[];
  };

  // 默认参数
  defaultParams: {
    temperature: number;
    maxTokens: number;
    topP: number;
    frequencyPenalty: number;
    presencePenalty: number;
  };

  // 限制配置
  limits: {
    maxRequestsPerMinute: number;
    maxTokensPerRequest: number;
    maxConcurrentRequests: number;
  };

  // 状态信息
  status: {
    isActive: boolean;
    lastTested: Date;
    isHealthy: boolean;
    errorCount: number;
    lastError?: string;
  };

  createdAt: Date;
  updatedAt: Date;
}

enum AIProvider {
  OPENAI = 'openai',
  CLAUDE = 'claude',
  GEMINI = 'gemini',
  MODELSCOPE = 'modelscope',
  OLLAMA = 'ollama',
  SILICONFLOW = 'siliconflow',
  CUSTOM = 'custom'
}

// 窗口状态
interface WindowState {
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  isMaximized: boolean;
  isFullScreen: boolean;
  displayId: string;

  // 面板状态
  panels: {
    sidebar: {
      visible: boolean;
      width: number;
      activeTab: string;
    };
    bottomPanel: {
      visible: boolean;
      height: number;
      activeTab: string;
    };
    rightPanel: {
      visible: boolean;
      width: number;
      activeTab: string;
    };
  };

  // 分割器位置
  splitters: {
    mainHorizontal: number;
    mainVertical: number;
    editorVertical: number;
  };
}

// 快捷键配置
interface KeyboardShortcuts {
  global: Record<string, string>;
  editor: Record<string, string>;
  navigation: Record<string, string>;
  ai: Record<string, string>;
}

// 插件设置
interface PluginSettings {
  enabled: string[];
  disabled: string[];
  configs: Record<string, any>;
}

// 设置导出格式
interface SettingsExport {
  version: string;
  exportDate: Date;
  settings: ApplicationSettings;
  metadata: {
    appVersion: string;
    platform: string;
    exportedBy: string;
  };
}

// API测试结果
interface APITestResult {
  success: boolean;
  responseTime: number;
  error?: string;
  details: {
    connectionTest: boolean;
    authenticationTest: boolean;
    modelListTest: boolean;
    generationTest: boolean;
  };
  timestamp: Date;
}

// 设置管理器
class SettingsManager {
  private settings: ApplicationSettings;
  private settingsPath: string;
  private watchers: Map<string, Function[]> = new Map();

  constructor(settingsPath: string) {
    this.settingsPath = settingsPath;
    this.settings = this.getDefaultSettings();
  }

  async loadSettings(): Promise<void> {
    try {
      const data = await this.readSettingsFile();
      this.settings = this.migrateSettings(data);
      await this.validateAndRepairSettings();
    } catch (error) {
      console.warn('Failed to load settings, using defaults:', error);
      this.settings = this.getDefaultSettings();
    }
  }

  async saveSettings(): Promise<void> {
    try {
      this.settings.metadata.lastUpdated = new Date();
      await this.writeSettingsFile(this.settings);
      this.notifyWatchers('*');
    } catch (error) {
      throw new Error(`Failed to save settings: ${error.message}`);
    }
  }

  async getSetting<T>(key: string): Promise<T> {
    const value = this.getNestedValue(this.settings, key);
    return value as T;
  }

  async updateSetting<T>(key: string, value: T): Promise<void> {
    this.setNestedValue(this.settings, key, value);
    await this.saveSettings();
    this.notifyWatchers(key);
  }

  async updateSettings(updates: Partial<ApplicationSettings>): Promise<void> {
    this.settings = { ...this.settings, ...updates };
    await this.saveSettings();
  }

  async resetSettings(keys?: string[]): Promise<void> {
    if (keys) {
      const defaults = this.getDefaultSettings();
      keys.forEach(key => {
        const defaultValue = this.getNestedValue(defaults, key);
        this.setNestedValue(this.settings, key, defaultValue);
      });
    } else {
      this.settings = this.getDefaultSettings();
    }

    await this.saveSettings();
  }

  // 监听设置变化
  watchSetting(key: string, callback: Function): () => void {
    if (!this.watchers.has(key)) {
      this.watchers.set(key, []);
    }
    this.watchers.get(key)!.push(callback);

    // 返回取消监听的函数
    return () => {
      const callbacks = this.watchers.get(key);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  private notifyWatchers(key: string): void {
    // 通知特定键的监听器
    const callbacks = this.watchers.get(key) || [];
    callbacks.forEach(callback => {
      try {
        callback(this.getNestedValue(this.settings, key));
      } catch (error) {
        console.error('Error in settings watcher:', error);
      }
    });

    // 通知全局监听器
    if (key !== '*') {
      const globalCallbacks = this.watchers.get('*') || [];
      globalCallbacks.forEach(callback => {
        try {
          callback(this.settings);
        } catch (error) {
          console.error('Error in global settings watcher:', error);
        }
      });
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!(key in current)) {
        current[key] = {};
      }
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  private getDefaultSettings(): ApplicationSettings {
    return {
      apiConfigs: [],
      defaultAPIConfig: '',
      preferences: {
        ui: {
          theme: 'auto',
          language: 'zh-CN',
          fontSize: 14,
          fontFamily: 'Microsoft YaHei',
          compactMode: false,
          showLineNumbers: true,
          wordWrap: true
        },
        editor: {
          autoSave: true,
          autoSaveInterval: 30,
          tabSize: 2,
          insertSpaces: true,
          showWhitespace: false,
          highlightCurrentLine: true,
          enableCodeFolding: true
        },
        aiGeneration: {
          defaultModel: '',
          defaultTemperature: 0.7,
          defaultMaxTokens: 2000,
          enableContextOptimization: true,
          autoDetectLanguage: true,
          enableAIFlavorReduction: true
        },
        project: {
          defaultProjectPath: '',
          autoBackup: true,
          backupInterval: 10,
          maxBackupFiles: 10,
          enableVersionControl: false
        },
        performance: {
          enableHardwareAcceleration: true,
          maxMemoryUsage: 1024,
          enableLazyLoading: true,
          cacheSize: 256
        },
        privacy: {
          enableTelemetry: false,
          enableCrashReporting: true,
          enableUsageAnalytics: false,
          dataRetentionDays: 30
        }
      },
      windowState: {
        bounds: { x: 100, y: 100, width: 1200, height: 800 },
        isMaximized: false,
        isFullScreen: false,
        displayId: 'primary',
        panels: {
          sidebar: { visible: true, width: 250, activeTab: 'outline' },
          bottomPanel: { visible: false, height: 200, activeTab: 'logs' },
          rightPanel: { visible: false, width: 300, activeTab: 'analysis' }
        },
        splitters: {
          mainHorizontal: 0.7,
          mainVertical: 0.8,
          editorVertical: 0.6
        }
      },
      shortcuts: {
        global: {},
        editor: {},
        navigation: {},
        ai: {}
      },
      plugins: {
        enabled: [],
        disabled: [],
        configs: {}
      },
      metadata: {
        version: '1.0.0',
        lastUpdated: new Date(),
        migrationVersion: 1
      }
    };
  }

  private async readSettingsFile(): Promise<any> {
    // 读取设置文件
    // 实际实现中使用 fs 模块
    return {};
  }

  private async writeSettingsFile(settings: ApplicationSettings): Promise<void> {
    // 写入设置文件
    // 实际实现中使用 fs 模块
  }

  private migrateSettings(data: any): ApplicationSettings {
    // 设置迁移逻辑
    // 处理版本升级时的设置兼容性
    return data;
  }

  private async validateAndRepairSettings(): Promise<void> {
    // 验证和修复设置
    // 确保所有必需的设置项都存在
  }
}

// API配置管理器
class APIConfigManager {
  private configs: Map<string, APIConfig> = new Map();

  async addAPIConfig(config: Partial<APIConfig>): Promise<string> {
    const apiConfig: APIConfig = {
      id: this.generateId(),
      name: config.name || '未命名配置',
      provider: config.provider || AIProvider.CUSTOM,
      connection: {
        apiKey: config.connection?.apiKey || '',
        apiUrl: config.connection?.apiUrl || '',
        timeout: config.connection?.timeout || 30000,
        retryAttempts: config.connection?.retryAttempts || 3,
        retryDelay: config.connection?.retryDelay || 1000
      },
      models: {
        textGeneration: config.models?.textGeneration || [],
        embedding: config.models?.embedding || [],
        analysis: config.models?.analysis || []
      },
      defaultParams: {
        temperature: config.defaultParams?.temperature || 0.7,
        maxTokens: config.defaultParams?.maxTokens || 2000,
        topP: config.defaultParams?.topP || 1.0,
        frequencyPenalty: config.defaultParams?.frequencyPenalty || 0,
        presencePenalty: config.defaultParams?.presencePenalty || 0
      },
      limits: {
        maxRequestsPerMinute: config.limits?.maxRequestsPerMinute || 60,
        maxTokensPerRequest: config.limits?.maxTokensPerRequest || 4000,
        maxConcurrentRequests: config.limits?.maxConcurrentRequests || 5
      },
      status: {
        isActive: true,
        lastTested: new Date(),
        isHealthy: false,
        errorCount: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.configs.set(apiConfig.id, apiConfig);
    return apiConfig.id;
  }

  async testAPIConfig(configId: string): Promise<APITestResult> {
    const config = this.configs.get(configId);
    if (!config) {
      throw new Error(`API配置不存在: ${configId}`);
    }

    const startTime = Date.now();
    const result: APITestResult = {
      success: false,
      responseTime: 0,
      details: {
        connectionTest: false,
        authenticationTest: false,
        modelListTest: false,
        generationTest: false
      },
      timestamp: new Date()
    };

    try {
      // 连接测试
      result.details.connectionTest = await this.testConnection(config);

      // 认证测试
      if (result.details.connectionTest) {
        result.details.authenticationTest = await this.testAuthentication(config);
      }

      // 模型列表测试
      if (result.details.authenticationTest) {
        result.details.modelListTest = await this.testModelList(config);
      }

      // 生成测试
      if (result.details.modelListTest) {
        result.details.generationTest = await this.testGeneration(config);
      }

      result.success = Object.values(result.details).every(test => test);
      result.responseTime = Date.now() - startTime;

      // 更新配置状态
      config.status.isHealthy = result.success;
      config.status.lastTested = new Date();
      if (!result.success) {
        config.status.errorCount++;
      } else {
        config.status.errorCount = 0;
      }

    } catch (error) {
      result.error = error.message;
      result.responseTime = Date.now() - startTime;

      config.status.isHealthy = false;
      config.status.lastError = error.message;
      config.status.errorCount++;
    }

    return result;
  }

  private async testConnection(config: APIConfig): Promise<boolean> {
    // 实现连接测试
    return true;
  }

  private async testAuthentication(config: APIConfig): Promise<boolean> {
    // 实现认证测试
    return true;
  }

  private async testModelList(config: APIConfig): Promise<boolean> {
    // 实现模型列表测试
    return true;
  }

  private async testGeneration(config: APIConfig): Promise<boolean> {
    // 实现生成测试
    return true;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

// 智能API地址检测器
class APIAddressDetector {
  private knownEndpoints: Map<AIProvider, string[]> = new Map();

  constructor() {
    this.initializeKnownEndpoints();
  }

  async detectAndCorrectAPIAddress(provider: AIProvider, inputUrl: string): Promise<{
    correctedUrl: string;
    confidence: number;
    suggestions: string[];
  }> {
    const knownUrls = this.knownEndpoints.get(provider) || [];

    // 直接匹配
    if (knownUrls.includes(inputUrl)) {
      return {
        correctedUrl: inputUrl,
        confidence: 1.0,
        suggestions: []
      };
    }

    // 模糊匹配
    const bestMatch = this.findBestMatch(inputUrl, knownUrls);
    if (bestMatch.confidence > 0.8) {
      return {
        correctedUrl: bestMatch.url,
        confidence: bestMatch.confidence,
        suggestions: knownUrls.filter(url => url !== bestMatch.url).slice(0, 3)
      };
    }

    // 返回建议
    return {
      correctedUrl: inputUrl,
      confidence: 0,
      suggestions: knownUrls.slice(0, 5)
    };
  }

  private initializeKnownEndpoints(): void {
    this.knownEndpoints.set(AIProvider.OPENAI, [
      'https://api.openai.com/v1',
      'https://api.openai.com',
      'https://openai.api2d.net/v1'
    ]);

    this.knownEndpoints.set(AIProvider.CLAUDE, [
      'https://api.anthropic.com/v1',
      'https://api.anthropic.com'
    ]);

    this.knownEndpoints.set(AIProvider.GEMINI, [
      'https://generativelanguage.googleapis.com/v1',
      'https://ai.google.dev/v1'
    ]);

    // 添加其他提供商的端点
  }

  private findBestMatch(input: string, candidates: string[]): { url: string; confidence: number } {
    let bestMatch = { url: input, confidence: 0 };

    for (const candidate of candidates) {
      const confidence = this.calculateSimilarity(input, candidate);
      if (confidence > bestMatch.confidence) {
        bestMatch = { url: candidate, confidence };
      }
    }

    return bestMatch;
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // 实现字符串相似度计算
    // 可以使用编辑距离、余弦相似度等算法
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }
}
```

### 5.14 运行日志功能模块

#### 5.14.1 功能概述
运行日志功能记录应用的运行状态、用户操作、错误信息等，便于问题诊断和用户支持。

#### 5.14.2 核心功能
- **操作日志**: 记录用户的关键操作
- **错误日志**: 记录系统错误和异常
- **性能日志**: 记录性能相关数据
- **AI调用日志**: 记录AI API调用情况
- **日志管理**: 日志文件的清理和归档
- **纯中文显示**: 应用运行日志、实时日志都要纯中文显示，不使用英文术语

#### 5.14.3 日志系统设计
```javascript
const logSystem = {
  // 日志级别
  levels: {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3
  },

  // 日志记录
  log: (level, category, message, data = null) => {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level: Object.keys(logSystem.levels)[level],
      category,
      message,
      data,
      sessionId: getSessionId()
    };

    // 写入日志文件
    writeToLogFile(logEntry);
  },

  // AI调用日志
  logAiCall: (provider, model, prompt, response, duration) => {
    logSystem.log(logSystem.levels.INFO, 'AI调用', 'AI模型调用', {
      提供商: provider,
      模型: model,
      提示词长度: prompt?.length,
      响应长度: response?.length,
      耗时: duration + '毫秒',
      调用成功: !!response ? '是' : '否'
    });
  },

  // 中文化日志消息
  chineseMessages: {
    levels: {
      ERROR: '错误',
      WARN: '警告',
      INFO: '信息',
      DEBUG: '调试'
    },

    categories: {
      'USER_ACTION': '用户操作',
      'SYSTEM_ERROR': '系统错误',
      'AI_CALL': 'AI调用',
      'FILE_OPERATION': '文件操作',
      'NETWORK_REQUEST': '网络请求',
      'DATABASE_OPERATION': '数据库操作'
    },

    // 常用操作消息
    operations: {
      'CREATE_PROJECT': '创建项目',
      'SAVE_PROJECT': '保存项目',
      'LOAD_PROJECT': '加载项目',
      'GENERATE_OUTLINE': '生成大纲',
      'GENERATE_CHAPTER': '生成章节',
      'EDIT_CHARACTER': '编辑角色',
      'ANALYZE_CHAPTER': '分析章节',
      'EXPORT_CONTENT': '导出内容'
    }
  },

  // 格式化中文日志
  formatChineseLog: (logEntry) => {
    const chineseLevel = logSystem.chineseMessages.levels[logEntry.level] || logEntry.level;
    const chineseCategory = logSystem.chineseMessages.categories[logEntry.category] || logEntry.category;

    return {
      时间: new Date(logEntry.timestamp).toLocaleString('zh-CN'),
      级别: chineseLevel,
      分类: chineseCategory,
      消息: logEntry.message,
      详情: logEntry.data
    };
  }
};
```

#### 5.14.4 技术实现
```typescript
// 日志服务接口
interface LoggingService {
  // 记录日志
  log(level: LogLevel, category: LogCategory, message: string, data?: any): Promise<void>;

  // 获取日志
  getLogs(filter: LogFilter): Promise<LogEntry[]>;

  // 清理日志
  cleanupLogs(options: CleanupOptions): Promise<number>;

  // 导出日志
  exportLogs(filter: LogFilter, format: 'json' | 'csv' | 'txt'): Promise<Blob>;

  // 日志统计
  getLogStatistics(timeRange: TimeRange): Promise<LogStatistics>;

  // 实时日志流
  subscribeToLogs(filter: LogFilter, callback: (log: LogEntry) => void): () => void;

  // 日志配置
  updateLogConfig(config: LogConfig): Promise<void>;
  getLogConfig(): Promise<LogConfig>;
}

// 日志级别
enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal'
}

// 日志分类
enum LogCategory {
  SYSTEM = 'system',           // 系统日志
  USER_ACTION = 'user_action', // 用户操作
  AI_API = 'ai_api',          // AI接口调用
  PERFORMANCE = 'performance', // 性能日志
  ERROR = 'error',            // 错误日志
  SECURITY = 'security',      // 安全日志
  DATABASE = 'database',      // 数据库操作
  FILE_IO = 'file_io',        // 文件操作
  NETWORK = 'network',        // 网络请求
  PLUGIN = 'plugin'           // 插件日志
}

// 日志条目
interface LogEntry {
  id: string;
  timestamp: Date;
  level: LogLevel;
  category: LogCategory;
  message: string;
  data?: any;

  // 上下文信息
  context: {
    userId?: string;
    projectId?: string;
    sessionId: string;
    version: string;
    platform: string;
  };

  // 性能信息
  performance?: {
    duration?: number;
    memoryUsage?: number;
    cpuUsage?: number;
  };

  // 错误信息
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };

  // 位置信息
  location?: {
    file: string;
    function: string;
    line: number;
  };
}

// 日志过滤器
interface LogFilter {
  levels?: LogLevel[];
  categories?: LogCategory[];
  timeRange?: TimeRange;
  searchText?: string;
  userId?: string;
  projectId?: string;
  limit?: number;
  offset?: number;
}

// 时间范围
interface TimeRange {
  startTime: Date;
  endTime: Date;
}

// 清理选项
interface CleanupOptions {
  olderThan: Date;
  levels?: LogLevel[];
  categories?: LogCategory[];
  keepCount?: number; // 保留最新的N条记录
}

// 日志统计
interface LogStatistics {
  totalLogs: number;
  levelCounts: Record<LogLevel, number>;
  categoryCounts: Record<LogCategory, number>;
  errorRate: number;
  averageResponseTime: number;
  topErrors: ErrorSummary[];
  timeDistribution: TimeDistribution[];
}

interface ErrorSummary {
  error: string;
  count: number;
  lastOccurrence: Date;
  affectedUsers: number;
}

interface TimeDistribution {
  hour: number;
  count: number;
}

// 日志配置
interface LogConfig {
  // 日志级别配置
  levels: {
    console: LogLevel;
    file: LogLevel;
    remote?: LogLevel;
  };

  // 分类配置
  categories: {
    enabled: LogCategory[];
    disabled: LogCategory[];
  };

  // 存储配置
  storage: {
    maxFileSize: number;    // MB
    maxFiles: number;
    rotationInterval: number; // 小时
    compressionEnabled: boolean;
  };

  // 性能配置
  performance: {
    enablePerformanceLogging: boolean;
    performanceThreshold: number; // 毫秒
    memoryThreshold: number;      // MB
  };

  // 隐私配置
  privacy: {
    maskSensitiveData: boolean;
    sensitiveFields: string[];
    retentionDays: number;
  };

  // 输出配置
  outputs: {
    console: boolean;
    file: boolean;
    remote?: {
      enabled: boolean;
      endpoint: string;
      apiKey: string;
    };
  };
}

// 日志管理器
class LogManager {
  private config: LogConfig;
  private outputs: LogOutput[] = [];
  private subscribers: Map<string, LogSubscriber> = new Map();
  private buffer: LogEntry[] = [];
  private bufferSize = 100;
  private flushInterval = 5000; // 5秒

  constructor(config: LogConfig) {
    this.config = config;
    this.initializeOutputs();
    this.startBufferFlush();
  }

  async log(level: LogLevel, category: LogCategory, message: string, data?: any): Promise<void> {
    // 检查是否应该记录此日志
    if (!this.shouldLog(level, category)) {
      return;
    }

    // 创建日志条目
    const logEntry: LogEntry = {
      id: this.generateId(),
      timestamp: new Date(),
      level,
      category,
      message: this.translateMessage(message), // 翻译为中文
      data: this.sanitizeData(data),
      context: this.getContext(),
      performance: this.getPerformanceInfo(),
      error: this.extractErrorInfo(data),
      location: this.getLocationInfo()
    };

    // 添加到缓冲区
    this.buffer.push(logEntry);

    // 通知订阅者
    this.notifySubscribers(logEntry);

    // 如果是高级别日志，立即刷新
    if (level === LogLevel.ERROR || level === LogLevel.FATAL) {
      await this.flushBuffer();
    }
  }

  private shouldLog(level: LogLevel, category: LogCategory): boolean {
    // 检查级别
    const levelPriority = this.getLevelPriority(level);
    const minPriority = this.getLevelPriority(this.config.levels.file);

    if (levelPriority < minPriority) {
      return false;
    }

    // 检查分类
    if (this.config.categories.disabled.includes(category)) {
      return false;
    }

    if (this.config.categories.enabled.length > 0 &&
        !this.config.categories.enabled.includes(category)) {
      return false;
    }

    return true;
  }

  private translateMessage(message: string): string {
    // 将英文日志消息翻译为中文
    const translations: Record<string, string> = {
      'Application started': '应用程序已启动',
      'User logged in': '用户已登录',
      'Project created': '项目已创建',
      'Chapter generated': '章节已生成',
      'AI API call failed': 'AI接口调用失败',
      'Database connection error': '数据库连接错误',
      'File save successful': '文件保存成功',
      'Memory usage high': '内存使用率过高',
      'Network request timeout': '网络请求超时',
      'Plugin loaded': '插件已加载'
    };

    return translations[message] || message;
  }

  private sanitizeData(data: any): any {
    if (!this.config.privacy.maskSensitiveData || !data) {
      return data;
    }

    // 深拷贝数据
    const sanitized = JSON.parse(JSON.stringify(data));

    // 遮蔽敏感字段
    this.config.privacy.sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '***';
      }
    });

    return sanitized;
  }

  private getContext(): any {
    return {
      sessionId: this.getSessionId(),
      version: this.getAppVersion(),
      platform: this.getPlatform(),
      userId: this.getCurrentUserId(),
      projectId: this.getCurrentProjectId()
    };
  }

  private getPerformanceInfo(): any {
    if (!this.config.performance.enablePerformanceLogging) {
      return undefined;
    }

    return {
      memoryUsage: this.getMemoryUsage(),
      cpuUsage: this.getCPUUsage(),
      duration: this.getLastOperationDuration()
    };
  }

  private extractErrorInfo(data: any): any {
    if (data instanceof Error) {
      return {
        name: data.name,
        message: data.message,
        stack: data.stack,
        code: (data as any).code
      };
    }

    if (data && data.error instanceof Error) {
      return this.extractErrorInfo(data.error);
    }

    return undefined;
  }

  private getLocationInfo(): any {
    // 获取调用位置信息
    const stack = new Error().stack;
    if (stack) {
      const lines = stack.split('\n');
      const callerLine = lines[3]; // 跳过当前函数和log函数
      const match = callerLine?.match(/at (.+) \((.+):(\d+):\d+\)/);

      if (match) {
        return {
          function: match[1],
          file: match[2],
          line: parseInt(match[3])
        };
      }
    }

    return undefined;
  }

  private async flushBuffer(): Promise<void> {
    if (this.buffer.length === 0) {
      return;
    }

    const logsToFlush = [...this.buffer];
    this.buffer = [];

    // 并行写入到所有输出
    const writePromises = this.outputs.map(output =>
      output.write(logsToFlush).catch(error =>
        console.error('日志输出失败:', error)
      )
    );

    await Promise.all(writePromises);
  }

  private startBufferFlush(): void {
    setInterval(() => {
      this.flushBuffer().catch(error =>
        console.error('定时刷新日志缓冲区失败:', error)
      );
    }, this.flushInterval);
  }

  private initializeOutputs(): void {
    // 控制台输出
    if (this.config.outputs.console) {
      this.outputs.push(new ConsoleLogOutput(this.config.levels.console));
    }

    // 文件输出
    if (this.config.outputs.file) {
      this.outputs.push(new FileLogOutput(this.config));
    }

    // 远程输出
    if (this.config.outputs.remote?.enabled) {
      this.outputs.push(new RemoteLogOutput(this.config.outputs.remote));
    }
  }

  private notifySubscribers(logEntry: LogEntry): void {
    this.subscribers.forEach(subscriber => {
      if (subscriber.filter(logEntry)) {
        subscriber.callback(logEntry);
      }
    });
  }

  private getLevelPriority(level: LogLevel): number {
    const priorities = {
      [LogLevel.DEBUG]: 0,
      [LogLevel.INFO]: 1,
      [LogLevel.WARN]: 2,
      [LogLevel.ERROR]: 3,
      [LogLevel.FATAL]: 4
    };
    return priorities[level];
  }

  // 辅助方法
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private getSessionId(): string {
    return 'session-' + Date.now();
  }

  private getAppVersion(): string {
    return '1.0.0';
  }

  private getPlatform(): string {
    return process.platform;
  }

  private getCurrentUserId(): string | undefined {
    return undefined; // 实际实现中获取当前用户ID
  }

  private getCurrentProjectId(): string | undefined {
    return undefined; // 实际实现中获取当前项目ID
  }

  private getMemoryUsage(): number {
    return process.memoryUsage().heapUsed / 1024 / 1024; // MB
  }

  private getCPUUsage(): number {
    return 0; // 实际实现中获取CPU使用率
  }

  private getLastOperationDuration(): number {
    return 0; // 实际实现中获取上次操作耗时
  }
}

// 日志输出接口
interface LogOutput {
  write(logs: LogEntry[]): Promise<void>;
}

// 控制台日志输出
class ConsoleLogOutput implements LogOutput {
  constructor(private minLevel: LogLevel) {}

  async write(logs: LogEntry[]): Promise<void> {
    logs.forEach(log => {
      if (this.shouldOutput(log.level)) {
        const message = this.formatMessage(log);

        switch (log.level) {
          case LogLevel.DEBUG:
            console.debug(message);
            break;
          case LogLevel.INFO:
            console.info(message);
            break;
          case LogLevel.WARN:
            console.warn(message);
            break;
          case LogLevel.ERROR:
          case LogLevel.FATAL:
            console.error(message);
            break;
        }
      }
    });
  }

  private shouldOutput(level: LogLevel): boolean {
    const priorities = {
      [LogLevel.DEBUG]: 0,
      [LogLevel.INFO]: 1,
      [LogLevel.WARN]: 2,
      [LogLevel.ERROR]: 3,
      [LogLevel.FATAL]: 4
    };

    return priorities[level] >= priorities[this.minLevel];
  }

  private formatMessage(log: LogEntry): string {
    const timestamp = log.timestamp.toISOString();
    const level = log.level.toUpperCase();
    const category = this.translateCategory(log.category);

    return `[${timestamp}] [${level}] [${category}] ${log.message}`;
  }

  private translateCategory(category: LogCategory): string {
    const translations = {
      [LogCategory.SYSTEM]: '系统',
      [LogCategory.USER_ACTION]: '用户操作',
      [LogCategory.AI_API]: 'AI接口',
      [LogCategory.PERFORMANCE]: '性能',
      [LogCategory.ERROR]: '错误',
      [LogCategory.SECURITY]: '安全',
      [LogCategory.DATABASE]: '数据库',
      [LogCategory.FILE_IO]: '文件操作',
      [LogCategory.NETWORK]: '网络',
      [LogCategory.PLUGIN]: '插件'
    };

    return translations[category] || category;
  }
}

// 文件日志输出
class FileLogOutput implements LogOutput {
  private currentFile: string;
  private fileSize: number = 0;

  constructor(private config: LogConfig) {
    this.currentFile = this.generateFileName();
  }

  async write(logs: LogEntry[]): Promise<void> {
    const content = logs.map(log => this.formatLogEntry(log)).join('\n') + '\n';

    // 检查是否需要轮转文件
    if (this.shouldRotateFile(content.length)) {
      await this.rotateFile();
    }

    // 写入文件
    await this.appendToFile(content);
    this.fileSize += content.length;
  }

  private formatLogEntry(log: LogEntry): string {
    return JSON.stringify({
      时间: log.timestamp.toISOString(),
      级别: log.level,
      分类: log.category,
      消息: log.message,
      数据: log.data,
      上下文: log.context,
      性能: log.performance,
      错误: log.error,
      位置: log.location
    });
  }

  private shouldRotateFile(additionalSize: number): boolean {
    const maxSize = this.config.storage.maxFileSize * 1024 * 1024; // 转换为字节
    return (this.fileSize + additionalSize) > maxSize;
  }

  private async rotateFile(): Promise<void> {
    // 压缩当前文件（如果启用）
    if (this.config.storage.compressionEnabled) {
      await this.compressFile(this.currentFile);
    }

    // 清理旧文件
    await this.cleanupOldFiles();

    // 创建新文件
    this.currentFile = this.generateFileName();
    this.fileSize = 0;
  }

  private generateFileName(): string {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
    return `logs/app-${dateStr}-${timeStr}.log`;
  }

  private async appendToFile(content: string): Promise<void> {
    // 实际实现中使用 fs.appendFile
  }

  private async compressFile(filePath: string): Promise<void> {
    // 实际实现中使用压缩库
  }

  private async cleanupOldFiles(): Promise<void> {
    // 实际实现中清理超过保留数量的旧文件
  }
}

// 远程日志输出
class RemoteLogOutput implements LogOutput {
  constructor(private config: { endpoint: string; apiKey: string }) {}

  async write(logs: LogEntry[]): Promise<void> {
    try {
      const response = await fetch(this.config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify({ logs })
      });

      if (!response.ok) {
        throw new Error(`远程日志发送失败: ${response.statusText}`);
      }
    } catch (error) {
      // 远程日志失败时，回退到本地记录
      console.error('远程日志发送失败:', error);
    }
  }
}

// 日志订阅者
interface LogSubscriber {
  filter: (log: LogEntry) => boolean;
  callback: (log: LogEntry) => void;
}

// 日志查询器
class LogQueryEngine {
  async queryLogs(filter: LogFilter): Promise<LogEntry[]> {
    // 实现日志查询逻辑
    // 支持复杂的过滤和搜索
    return [];
  }

  async getLogStatistics(timeRange: TimeRange): Promise<LogStatistics> {
    // 实现日志统计逻辑
    return {
      totalLogs: 0,
      levelCounts: {} as any,
      categoryCounts: {} as any,
      errorRate: 0,
      averageResponseTime: 0,
      topErrors: [],
      timeDistribution: []
    };
  }
}
```

### 5.15 向量库检索功能模块

#### 5.15.1 功能概述
向量库检索功能基于语义相似度进行内容检索，帮助用户快速找到相关的章节内容、角色信息和情节片段。

#### 5.15.2 核心功能
- **语义检索**: 基于文本语义而非关键词的智能检索
- **嵌入模型配置**: 支持多种文本嵌入模型
- **检索范围控制**: 可指定检索范围和内容类型
- **相似度排序**: 按语义相似度对结果排序
- **索引管理**: 自动构建和维护向量索引

#### 5.15.3 嵌入模型配置
```javascript
const embeddingModels = {
  openai: {
    name: "OpenAI Embeddings",
    models: [
      {
        id: "text-embedding-3-large",
        name: "Text Embedding 3 Large",
        dimensions: 3072,
        maxTokens: 8191,
        description: "最新的大型嵌入模型，性能最佳"
      },
      {
        id: "text-embedding-3-small",
        name: "Text Embedding 3 Small",
        dimensions: 1536,
        maxTokens: 8191,
        description: "轻量级嵌入模型，速度快"
      },
      {
        id: "text-embedding-ada-002",
        name: "Ada v2",
        dimensions: 1536,
        maxTokens: 8191,
        description: "经典嵌入模型，稳定可靠"
      }
    ],
    apiEndpoint: "https://api.openai.com/v1/embeddings"
  },

  huggingface: {
    name: "Hugging Face Embeddings",
    models: [
      {
        id: "sentence-transformers/all-MiniLM-L6-v2",
        name: "All MiniLM L6 v2",
        dimensions: 384,
        description: "轻量级多语言模型"
      },
      {
        id: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        name: "Paraphrase Multilingual MiniLM",
        dimensions: 384,
        description: "多语言释义模型"
      },
      {
        id: "BAAI/bge-large-zh-v1.5",
        name: "BGE Large Chinese",
        dimensions: 1024,
        description: "中文优化的大型嵌入模型"
      }
    ],
    apiEndpoint: "https://api-inference.huggingface.co/pipeline/feature-extraction"
  },

  local: {
    name: "本地嵌入模型",
    models: [
      {
        id: "all-MiniLM-L6-v2",
        name: "本地 MiniLM",
        dimensions: 384,
        description: "本地部署的轻量级模型"
      }
    ],
    apiEndpoint: "http://localhost:8080/embeddings"
  }
};
```

#### 5.15.4 检索实现机制
```javascript
const vectorSearch = {
  // 构建向量索引
  buildIndex: async (documents) => {
    const embeddings = await Promise.all(
      documents.map(doc => generateEmbedding(doc.content))
    );

    return {
      documents,
      embeddings,
      index: createVectorIndex(embeddings)
    };
  },

  // 语义检索
  search: async (query, options = {}) => {
    const queryEmbedding = await generateEmbedding(query);
    const similarities = calculateSimilarities(queryEmbedding, vectorIndex.embeddings);

    return similarities
      .map((score, index) => ({
        document: vectorIndex.documents[index],
        score,
        relevance: score > options.threshold ? 'high' : 'medium'
      }))
      .filter(result => result.score > (options.threshold || 0.3))
      .sort((a, b) => b.score - a.score)
      .slice(0, options.limit || 10);
  }
};
```

#### 5.15.5 技术实现
```typescript
// 向量库检索服务接口
interface VectorSearchService {
  // 初始化向量库
  initializeVectorStore(projectId: string): Promise<void>;

  // 添加文档到向量库
  addDocument(projectId: string, document: DocumentInput): Promise<string>;

  // 批量添加文档
  addDocuments(projectId: string, documents: DocumentInput[]): Promise<string[]>;

  // 更新文档
  updateDocument(projectId: string, documentId: string, updates: Partial<DocumentInput>): Promise<void>;

  // 删除文档
  deleteDocument(projectId: string, documentId: string): Promise<void>;

  // 语义搜索
  semanticSearch(projectId: string, query: string, options: SearchOptions): Promise<SearchResult[]>;

  // 相似文档搜索
  findSimilarDocuments(projectId: string, documentId: string, options: SimilarityOptions): Promise<SearchResult[]>;

  // 重建索引
  rebuildIndex(projectId: string): Promise<void>;

  // 获取索引统计
  getIndexStatistics(projectId: string): Promise<IndexStatistics>;

  // 配置嵌入模型
  configureEmbeddingModel(config: EmbeddingModelConfig): Promise<void>;

  // 清理向量库
  clearVectorStore(projectId: string): Promise<void>;
}

// 文档输入
interface DocumentInput {
  id?: string;
  content: string;
  metadata: DocumentMetadata;
  type: DocumentType;
  chunkSize?: number;
  overlapSize?: number;
}

// 文档类型
enum DocumentType {
  CHAPTER = 'chapter',
  CHARACTER = 'character',
  OUTLINE = 'outline',
  WORLDBUILDING = 'worldbuilding',
  DIALOGUE = 'dialogue',
  DESCRIPTION = 'description',
  PLOT_POINT = 'plot_point'
}

// 文档元数据
interface DocumentMetadata {
  title: string;
  chapterId?: string;
  characterId?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  wordCount: number;
  importance: 'high' | 'medium' | 'low';

  // 内容特征
  features?: {
    hasDialogue: boolean;
    hasAction: boolean;
    hasDescription: boolean;
    emotionalTone: string;
    mainCharacters: string[];
  };
}

// 搜索选项
interface SearchOptions {
  limit?: number;
  threshold?: number;
  documentTypes?: DocumentType[];
  timeRange?: {
    startDate: Date;
    endDate: Date;
  };
  metadata?: {
    tags?: string[];
    importance?: ('high' | 'medium' | 'low')[];
    characters?: string[];
  };
  rerank?: boolean;
  includeMetadata?: boolean;
}

// 相似性选项
interface SimilarityOptions {
  limit?: number;
  threshold?: number;
  excludeSelf?: boolean;
  sameTypeOnly?: boolean;
}

// 搜索结果
interface SearchResult {
  documentId: string;
  content: string;
  metadata: DocumentMetadata;
  similarity: number;
  relevanceScore: number;

  // 匹配信息
  matchInfo: {
    matchedChunks: MatchedChunk[];
    highlightedContent: string;
    keyPhrases: string[];
  };

  // 上下文信息
  context?: {
    previousContent?: string;
    nextContent?: string;
    relatedDocuments?: string[];
  };
}

// 匹配的文档块
interface MatchedChunk {
  chunkId: string;
  content: string;
  startIndex: number;
  endIndex: number;
  similarity: number;
}

// 索引统计
interface IndexStatistics {
  totalDocuments: number;
  totalChunks: number;
  indexSize: number; // bytes
  lastUpdated: Date;

  // 文档类型分布
  documentTypeDistribution: Record<DocumentType, number>;

  // 向量维度信息
  vectorDimensions: number;
  embeddingModel: string;

  // 性能指标
  averageSearchTime: number;
  indexingTime: number;
}

// 嵌入模型配置
interface EmbeddingModelConfig {
  provider: EmbeddingProvider;
  modelName: string;
  dimensions: number;
  apiKey?: string;
  apiUrl?: string;
  batchSize?: number;
  timeout?: number;
}

enum EmbeddingProvider {
  OPENAI = 'openai',
  HUGGINGFACE = 'huggingface',
  SENTENCE_TRANSFORMERS = 'sentence_transformers',
  LOCAL = 'local',
  CUSTOM = 'custom'
}

// 向量文档
interface VectorDocument {
  id: string;
  content: string;
  vector: number[];
  metadata: DocumentMetadata;
  chunks: VectorChunk[];
  createdAt: Date;
  updatedAt: Date;
}

// 向量块
interface VectorChunk {
  id: string;
  content: string;
  vector: number[];
  startIndex: number;
  endIndex: number;
  parentDocumentId: string;
}

// 向量库管理器
class VectorStoreManager {
  private stores: Map<string, VectorStore> = new Map();
  private embeddingService: EmbeddingService;
  private chunkingService: ChunkingService;

  constructor(embeddingService: EmbeddingService) {
    this.embeddingService = embeddingService;
    this.chunkingService = new ChunkingService();
  }

  async initializeVectorStore(projectId: string): Promise<void> {
    if (this.stores.has(projectId)) {
      return;
    }

    const store = new VectorStore(projectId, this.embeddingService);
    await store.initialize();
    this.stores.set(projectId, store);
  }

  async addDocument(projectId: string, document: DocumentInput): Promise<string> {
    const store = await this.getStore(projectId);

    // 文档分块
    const chunks = await this.chunkingService.chunkDocument(
      document.content,
      document.chunkSize || 500,
      document.overlapSize || 50
    );

    // 生成嵌入向量
    const documentVector = await this.embeddingService.generateEmbedding(document.content);
    const chunkVectors = await Promise.all(
      chunks.map(chunk => this.embeddingService.generateEmbedding(chunk.content))
    );

    // 创建向量文档
    const vectorDocument: VectorDocument = {
      id: document.id || this.generateId(),
      content: document.content,
      vector: documentVector,
      metadata: document.metadata,
      chunks: chunks.map((chunk, index) => ({
        id: this.generateId(),
        content: chunk.content,
        vector: chunkVectors[index],
        startIndex: chunk.startIndex,
        endIndex: chunk.endIndex,
        parentDocumentId: document.id || this.generateId()
      })),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 添加到向量库
    await store.addDocument(vectorDocument);

    return vectorDocument.id;
  }

  async semanticSearch(projectId: string, query: string, options: SearchOptions): Promise<SearchResult[]> {
    const store = await this.getStore(projectId);

    // 生成查询向量
    const queryVector = await this.embeddingService.generateEmbedding(query);

    // 执行向量搜索
    const rawResults = await store.search(queryVector, {
      limit: options.limit || 10,
      threshold: options.threshold || 0.7,
      filters: this.buildFilters(options)
    });

    // 处理搜索结果
    const searchResults = await Promise.all(
      rawResults.map(result => this.processSearchResult(result, query, options))
    );

    // 重排序（如果启用）
    if (options.rerank) {
      return this.rerankResults(searchResults, query);
    }

    return searchResults;
  }

  async findSimilarDocuments(projectId: string, documentId: string, options: SimilarityOptions): Promise<SearchResult[]> {
    const store = await this.getStore(projectId);

    // 获取目标文档
    const targetDocument = await store.getDocument(documentId);
    if (!targetDocument) {
      throw new Error(`文档不存在: ${documentId}`);
    }

    // 使用文档向量进行搜索
    const rawResults = await store.search(targetDocument.vector, {
      limit: options.limit || 10,
      threshold: options.threshold || 0.8,
      excludeIds: options.excludeSelf ? [documentId] : [],
      typeFilter: options.sameTypeOnly ? targetDocument.metadata.type : undefined
    });

    // 处理结果
    return Promise.all(
      rawResults.map(result => this.processSearchResult(result, targetDocument.content, {}))
    );
  }

  private async getStore(projectId: string): Promise<VectorStore> {
    if (!this.stores.has(projectId)) {
      await this.initializeVectorStore(projectId);
    }
    return this.stores.get(projectId)!;
  }

  private buildFilters(options: SearchOptions): any {
    const filters: any = {};

    if (options.documentTypes) {
      filters.type = { $in: options.documentTypes };
    }

    if (options.timeRange) {
      filters.createdAt = {
        $gte: options.timeRange.startDate,
        $lte: options.timeRange.endDate
      };
    }

    if (options.metadata) {
      if (options.metadata.tags) {
        filters['metadata.tags'] = { $in: options.metadata.tags };
      }

      if (options.metadata.importance) {
        filters['metadata.importance'] = { $in: options.metadata.importance };
      }

      if (options.metadata.characters) {
        filters['metadata.features.mainCharacters'] = { $in: options.metadata.characters };
      }
    }

    return filters;
  }

  private async processSearchResult(rawResult: any, query: string, options: SearchOptions): Promise<SearchResult> {
    // 计算相关性评分
    const relevanceScore = this.calculateRelevanceScore(rawResult, query);

    // 生成匹配信息
    const matchInfo = await this.generateMatchInfo(rawResult, query);

    // 获取上下文信息
    const context = options.includeMetadata ?
      await this.getContextInfo(rawResult.id) : undefined;

    return {
      documentId: rawResult.id,
      content: rawResult.content,
      metadata: rawResult.metadata,
      similarity: rawResult.similarity,
      relevanceScore,
      matchInfo,
      context
    };
  }

  private calculateRelevanceScore(result: any, query: string): number {
    // 综合考虑向量相似度、文本匹配度、元数据相关性等因素
    let score = result.similarity * 0.6; // 向量相似度权重60%

    // 文本匹配度
    const textMatch = this.calculateTextMatch(result.content, query);
    score += textMatch * 0.3; // 文本匹配权重30%

    // 元数据相关性
    const metadataRelevance = this.calculateMetadataRelevance(result.metadata, query);
    score += metadataRelevance * 0.1; // 元数据权重10%

    return Math.min(1, score);
  }

  private calculateTextMatch(content: string, query: string): number {
    // 实现文本匹配度计算
    const queryWords = query.toLowerCase().split(/\s+/);
    const contentWords = content.toLowerCase().split(/\s+/);

    const matches = queryWords.filter(word =>
      contentWords.some(contentWord => contentWord.includes(word))
    );

    return matches.length / queryWords.length;
  }

  private calculateMetadataRelevance(metadata: DocumentMetadata, query: string): number {
    // 实现元数据相关性计算
    let relevance = 0;

    // 检查标题匹配
    if (metadata.title.toLowerCase().includes(query.toLowerCase())) {
      relevance += 0.5;
    }

    // 检查标签匹配
    const queryLower = query.toLowerCase();
    const tagMatches = metadata.tags.filter(tag =>
      tag.toLowerCase().includes(queryLower) || queryLower.includes(tag.toLowerCase())
    );
    relevance += (tagMatches.length / metadata.tags.length) * 0.3;

    // 重要性加权
    const importanceWeights = { high: 0.2, medium: 0.1, low: 0.05 };
    relevance += importanceWeights[metadata.importance];

    return Math.min(1, relevance);
  }

  private async generateMatchInfo(result: any, query: string): Promise<any> {
    // 生成匹配信息，包括高亮内容、关键短语等
    const keyPhrases = this.extractKeyPhrases(query);
    const highlightedContent = this.highlightMatches(result.content, keyPhrases);

    return {
      matchedChunks: result.chunks || [],
      highlightedContent,
      keyPhrases
    };
  }

  private extractKeyPhrases(query: string): string[] {
    // 提取查询中的关键短语
    return query.split(/[，。！？；：\s]+/).filter(phrase => phrase.length > 1);
  }

  private highlightMatches(content: string, keyPhrases: string[]): string {
    let highlighted = content;

    keyPhrases.forEach(phrase => {
      const regex = new RegExp(`(${phrase})`, 'gi');
      highlighted = highlighted.replace(regex, '<mark>$1</mark>');
    });

    return highlighted;
  }

  private async getContextInfo(documentId: string): Promise<any> {
    // 获取文档的上下文信息
    return {
      previousContent: '',
      nextContent: '',
      relatedDocuments: []
    };
  }

  private async rerankResults(results: SearchResult[], query: string): Promise<SearchResult[]> {
    // 使用更复杂的算法重新排序结果
    return results.sort((a, b) => {
      // 综合考虑相似度、相关性、重要性等因素
      const scoreA = a.similarity * 0.4 + a.relevanceScore * 0.4 +
                    (a.metadata.importance === 'high' ? 0.2 :
                     a.metadata.importance === 'medium' ? 0.1 : 0);

      const scoreB = b.similarity * 0.4 + b.relevanceScore * 0.4 +
                    (b.metadata.importance === 'high' ? 0.2 :
                     b.metadata.importance === 'medium' ? 0.1 : 0);

      return scoreB - scoreA;
    });
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

// 向量库实现
class VectorStore {
  private documents: Map<string, VectorDocument> = new Map();
  private index: VectorIndex;

  constructor(private projectId: string, private embeddingService: EmbeddingService) {
    this.index = new VectorIndex();
  }

  async initialize(): Promise<void> {
    await this.loadExistingDocuments();
    await this.buildIndex();
  }

  async addDocument(document: VectorDocument): Promise<void> {
    this.documents.set(document.id, document);
    await this.index.addVector(document.id, document.vector, document.metadata);

    // 添加文档块到索引
    for (const chunk of document.chunks) {
      await this.index.addVector(chunk.id, chunk.vector, {
        ...document.metadata,
        parentDocumentId: document.id,
        isChunk: true
      });
    }

    await this.persistDocument(document);
  }

  async search(queryVector: number[], options: any): Promise<any[]> {
    return this.index.search(queryVector, options);
  }

  async getDocument(documentId: string): Promise<VectorDocument | undefined> {
    return this.documents.get(documentId);
  }

  private async loadExistingDocuments(): Promise<void> {
    // 从持久化存储加载现有文档
  }

  private async buildIndex(): Promise<void> {
    // 构建向量索引
    for (const [id, document] of this.documents) {
      await this.index.addVector(id, document.vector, document.metadata);
    }
  }

  private async persistDocument(document: VectorDocument): Promise<void> {
    // 持久化文档到存储
  }
}

// 向量索引
class VectorIndex {
  private vectors: Map<string, { vector: number[]; metadata: any }> = new Map();

  async addVector(id: string, vector: number[], metadata: any): Promise<void> {
    this.vectors.set(id, { vector, metadata });
  }

  async search(queryVector: number[], options: any): Promise<any[]> {
    const results: Array<{ id: string; similarity: number; metadata: any }> = [];

    for (const [id, data] of this.vectors) {
      // 应用过滤器
      if (!this.matchesFilters(data.metadata, options.filters)) {
        continue;
      }

      // 计算余弦相似度
      const similarity = this.cosineSimilarity(queryVector, data.vector);

      if (similarity >= (options.threshold || 0)) {
        results.push({
          id,
          similarity,
          metadata: data.metadata
        });
      }
    }

    // 按相似度排序
    results.sort((a, b) => b.similarity - a.similarity);

    // 限制结果数量
    return results.slice(0, options.limit || 10);
  }

  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) {
      throw new Error('向量维度不匹配');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  private matchesFilters(metadata: any, filters: any): boolean {
    if (!filters) return true;

    for (const [key, condition] of Object.entries(filters)) {
      const value = this.getNestedValue(metadata, key);

      if (!this.evaluateCondition(value, condition)) {
        return false;
      }
    }

    return true;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private evaluateCondition(value: any, condition: any): boolean {
    if (typeof condition === 'object' && condition !== null) {
      if (condition.$in) {
        return Array.isArray(value) ?
          value.some(v => condition.$in.includes(v)) :
          condition.$in.includes(value);
      }

      if (condition.$gte && condition.$lte) {
        return value >= condition.$gte && value <= condition.$lte;
      }

      // 其他条件操作符...
    }

    return value === condition;
  }
}

// 嵌入服务
class EmbeddingService {
  constructor(private config: EmbeddingModelConfig) {}

  async generateEmbedding(text: string): Promise<number[]> {
    switch (this.config.provider) {
      case EmbeddingProvider.OPENAI:
        return this.generateOpenAIEmbedding(text);
      case EmbeddingProvider.HUGGINGFACE:
        return this.generateHuggingFaceEmbedding(text);
      case EmbeddingProvider.LOCAL:
        return this.generateLocalEmbedding(text);
      default:
        throw new Error(`不支持的嵌入提供商: ${this.config.provider}`);
    }
  }

  private async generateOpenAIEmbedding(text: string): Promise<number[]> {
    // 调用OpenAI嵌入API
    const response = await fetch(`${this.config.apiUrl}/embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.config.modelName,
        input: text
      })
    });

    const data = await response.json();
    return data.data[0].embedding;
  }

  private async generateHuggingFaceEmbedding(text: string): Promise<number[]> {
    // 调用HuggingFace嵌入API
    // 实现具体的API调用逻辑
    return [];
  }

  private async generateLocalEmbedding(text: string): Promise<number[]> {
    // 使用本地嵌入模型
    // 实现本地模型调用逻辑
    return [];
  }
}

// 文档分块服务
class ChunkingService {
  async chunkDocument(content: string, chunkSize: number, overlapSize: number): Promise<Array<{
    content: string;
    startIndex: number;
    endIndex: number;
  }>> {
    const chunks: Array<{ content: string; startIndex: number; endIndex: number }> = [];

    // 按句子分割
    const sentences = this.splitIntoSentences(content);

    let currentChunk = '';
    let currentStartIndex = 0;
    let sentenceStartIndex = 0;

    for (const sentence of sentences) {
      // 检查添加当前句子是否会超过块大小
      if (currentChunk.length + sentence.length > chunkSize && currentChunk.length > 0) {
        // 保存当前块
        chunks.push({
          content: currentChunk.trim(),
          startIndex: currentStartIndex,
          endIndex: sentenceStartIndex - 1
        });

        // 开始新块，包含重叠内容
        const overlapContent = this.getOverlapContent(currentChunk, overlapSize);
        currentChunk = overlapContent + sentence;
        currentStartIndex = sentenceStartIndex - overlapContent.length;
      } else {
        currentChunk += sentence;
        if (chunks.length === 0) {
          currentStartIndex = sentenceStartIndex;
        }
      }

      sentenceStartIndex += sentence.length;
    }

    // 添加最后一个块
    if (currentChunk.trim().length > 0) {
      chunks.push({
        content: currentChunk.trim(),
        startIndex: currentStartIndex,
        endIndex: content.length - 1
      });
    }

    return chunks;
  }

  private splitIntoSentences(text: string): string[] {
    // 按中文句号、问号、感叹号分割
    return text.split(/([。！？；])/g).filter(s => s.trim().length > 0);
  }

  private getOverlapContent(content: string, overlapSize: number): string {
    if (content.length <= overlapSize) {
      return content;
    }

    // 从末尾取重叠内容，尽量在句子边界处截断
    const overlap = content.slice(-overlapSize);
    const sentenceStart = overlap.search(/[。！？；]/);

    return sentenceStart > 0 ? overlap.slice(sentenceStart + 1) : overlap;
  }
}
```

### 5.16 上下文管理功能模块

#### 5.16.1 功能概述
上下文管理功能智能提取和管理小说创作过程中的上下文信息，确保AI生成内容的连贯性和一致性。

#### 5.16.2 核心功能
- **智能上下文提取**: 自动提取相关章节、角色、情节信息
- **上下文优化**: 根据token限制智能压缩上下文
- **关联分析**: 分析章节间的关联关系
- **伏笔检索**: 自动检索和跟踪故事中的伏笔线索
- **上下文缓存**: 缓存常用上下文提升性能
- **动态更新**: 实时更新上下文信息

#### 5.16.3 上下文提取策略
```javascript
const contextManager = {
  // 提取章节上下文
  extractChapterContext: (currentChapter, options = {}) => {
    const context = {
      previousChapters: [],
      characters: [],
      plotPoints: [],
      worldSettings: []
    };

    // 提取前序章节
    const prevCount = options.previousChapters || 2;
    context.previousChapters = getChapters(
      currentChapter.number - prevCount,
      currentChapter.number - 1
    );

    // 提取相关角色
    context.characters = extractRelevantCharacters(currentChapter);

    // 提取关键情节点
    context.plotPoints = extractPlotPoints(context.previousChapters);

    // 提取世界观设定
    context.worldSettings = extractWorldSettings(currentChapter.projectId);

    return context;
  },

  // 优化上下文长度
  optimizeContext: (context, maxTokens = 4000) => {
    let totalTokens = calculateTokens(context);

    if (totalTokens <= maxTokens) {
      return context;
    }

    // 按重要性压缩上下文
    const optimized = {
      ...context,
      previousChapters: compressChapters(context.previousChapters, maxTokens * 0.4),
      characters: compressCharacters(context.characters, maxTokens * 0.3),
      plotPoints: compressPlotPoints(context.plotPoints, maxTokens * 0.2),
      worldSettings: compressWorldSettings(context.worldSettings, maxTokens * 0.1)
    };

    return optimized;
  },

  // 伏笔检索和管理
  foreshadowingManager: {
    // 检索伏笔线索
    detectForeshadowing: (chapters) => {
      const foreshadowing = [];

      chapters.forEach((chapter, index) => {
        // 使用AI分析检测伏笔
        const detected = analyzeChapterForForeshadowing(chapter);

        detected.forEach(item => {
          foreshadowing.push({
            id: generateId(),
            chapterNumber: index + 1,
            chapterTitle: chapter.title,
            content: item.content,
            type: item.type, // 人物伏笔、情节伏笔、物品伏笔等
            confidence: item.confidence,
            keywords: item.keywords,
            context: item.context,
            status: 'pending', // pending, resolved, abandoned
            createdAt: new Date().toISOString()
          });
        });
      });

      return foreshadowing;
    },

    // 跟踪伏笔解决情况
    trackForeshadowingResolution: (foreshadowingList, newChapter) => {
      const updates = [];

      foreshadowingList.forEach(item => {
        if (item.status === 'pending') {
          // 检查新章节是否解决了这个伏笔
          const isResolved = checkForeshadowingResolution(item, newChapter);

          if (isResolved) {
            updates.push({
              ...item,
              status: 'resolved',
              resolvedInChapter: newChapter.number,
              resolvedAt: new Date().toISOString()
            });
          }
        }
      });

      return updates;
    },

    // 生成伏笔报告
    generateForeshadowingReport: (foreshadowingList) => {
      const report = {
        total: foreshadowingList.length,
        pending: foreshadowingList.filter(f => f.status === 'pending').length,
        resolved: foreshadowingList.filter(f => f.status === 'resolved').length,
        abandoned: foreshadowingList.filter(f => f.status === 'abandoned').length,
        byType: {},
        byChapter: {}
      };

      // 按类型统计
      foreshadowingList.forEach(item => {
        report.byType[item.type] = (report.byType[item.type] || 0) + 1;
        report.byChapter[item.chapterNumber] = (report.byChapter[item.chapterNumber] || 0) + 1;
      });

      return report;
    }
  }
};
```

#### 5.16.4 技术实现
```typescript
// 上下文管理服务接口
interface ContextManagerService {
  // 提取上下文
  extractContext(projectId: string, targetChapterId: string, options: ContextExtractionOptions): Promise<ExtractedContext>;

  // 优化上下文
  optimizeContext(context: ExtractedContext, constraints: ContextConstraints): Promise<OptimizedContext>;

  // 应用上下文
  applyContext(context: OptimizedContext, generationRequest: GenerationRequest): Promise<ContextualizedRequest>;

  // 上下文缓存管理
  cacheContext(key: string, context: ExtractedContext): Promise<void>;
  getCachedContext(key: string): Promise<ExtractedContext | null>;
  clearContextCache(projectId?: string): Promise<void>;

  // 上下文分析
  analyzeContextRelevance(context: ExtractedContext, target: string): Promise<RelevanceAnalysis>;

  // 上下文模板管理
  createContextTemplate(template: ContextTemplate): Promise<string>;
  getContextTemplates(): Promise<ContextTemplate[]>;
  applyContextTemplate(templateId: string, variables: Record<string, any>): Promise<ExtractedContext>;
}

// 上下文提取选项
interface ContextExtractionOptions {
  // 提取范围
  scope: {
    includePreviousChapters: number;    // 包含前面多少章
    includeFollowingChapters: number;   // 包含后面多少章
    includeCharacterProfiles: boolean;  // 包含角色档案
    includeWorldBuilding: boolean;      // 包含世界观设定
    includeOutline: boolean;            // 包含大纲信息
  };

  // 提取策略
  strategy: {
    prioritizeRecent: boolean;          // 优先考虑最近的内容
    focusOnMainCharacters: boolean;     // 重点关注主要角色
    includeDialogue: boolean;           // 包含对话内容
    includeNarration: boolean;          // 包含叙述内容
    includeDescriptions: boolean;       // 包含描述内容
  };

  // 过滤条件
  filters: {
    minRelevanceScore: number;          // 最小相关性评分
    excludePatterns: string[];          // 排除的模式
    includePatterns: string[];          // 包含的模式
    maxAge: number;                     // 最大时间跨度（天）
  };

  // 输出格式
  format: {
    includeMetadata: boolean;           // 包含元数据
    includeSourceReferences: boolean;   // 包含来源引用
    structuredOutput: boolean;          // 结构化输出
  };
}

// 提取的上下文
interface ExtractedContext {
  id: string;
  projectId: string;
  targetChapterId: string;
  extractionDate: Date;

  // 核心内容
  content: {
    // 故事背景
    background: {
      worldSetting: string;
      timeAndPlace: string;
      socialContext: string;
      previousEvents: string;
    };

    // 角色信息
    characters: {
      mainCharacters: ContextualCharacter[];
      supportingCharacters: ContextualCharacter[];
      relationships: CharacterRelationshipContext[];
    };

    // 情节信息
    plot: {
      currentSituation: string;
      ongoingConflicts: string[];
      unresolvedPlotlines: string[];
      foreshadowing: string[];
    };

    // 风格和语调
    style: {
      narrativeVoice: string;
      writingStyle: string;
      toneAndMood: string;
      pacing: string;
    };

    // 最近发展
    recentDevelopments: {
      lastChapterSummary: string;
      characterChanges: string[];
      plotProgression: string[];
      newElements: string[];
    };
  };

  // 元数据
  metadata: {
    sourceChapters: string[];
    relevanceScores: Record<string, number>;
    extractionMethod: string;
    confidence: number;
    totalTokens: number;
  };

  // 引用信息
  references: ContextReference[];
}

// 上下文中的角色信息
interface ContextualCharacter {
  characterId: string;
  name: string;
  currentState: string;
  recentActions: string[];
  emotionalState: string;
  goals: string[];
  relationships: string[];
  relevanceScore: number;
}

// 角色关系上下文
interface CharacterRelationshipContext {
  character1Id: string;
  character2Id: string;
  relationshipType: string;
  currentStatus: string;
  recentInteractions: string[];
  tensions: string[];
  relevanceScore: number;
}

// 上下文引用
interface ContextReference {
  sourceType: 'chapter' | 'character' | 'outline' | 'worldbuilding';
  sourceId: string;
  content: string;
  relevanceScore: number;
  extractedAt: Date;
}

// 上下文约束
interface ContextConstraints {
  maxTokens: number;
  maxCharacters: number;
  priorityWeights: {
    background: number;
    characters: number;
    plot: number;
    style: number;
    recent: number;
  };
  requiredElements: string[];
  optionalElements: string[];
}

// 优化后的上下文
interface OptimizedContext {
  originalContext: ExtractedContext;
  optimizedContent: string;

  // 优化统计
  optimization: {
    originalTokens: number;
    optimizedTokens: number;
    compressionRatio: number;
    retainedInformation: number; // 保留信息的百分比
  };

  // 优化策略
  appliedStrategies: OptimizationStrategy[];

  // 质量评估
  quality: {
    coherence: number;
    completeness: number;
    relevance: number;
    clarity: number;
  };
}

// 优化策略
interface OptimizationStrategy {
  name: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  tokensReduced: number;
  informationLoss: number;
}

// 上下文化的请求
interface ContextualizedRequest {
  originalRequest: GenerationRequest;
  enhancedPrompt: string;
  contextSummary: string;

  // 上下文应用信息
  application: {
    contextTokens: number;
    promptTokens: number;
    totalTokens: number;
    contextRatio: number;
  };

  // 预期效果
  expectedImprovements: string[];
}

// 生成请求
interface GenerationRequest {
  type: 'chapter' | 'dialogue' | 'description' | 'analysis';
  prompt: string;
  parameters: {
    temperature: number;
    maxTokens: number;
    topP: number;
  };
  targetChapterId: string;
  requirements: string[];
}

// 相关性分析
interface RelevanceAnalysis {
  overallRelevance: number;
  categoryRelevance: {
    background: number;
    characters: number;
    plot: number;
    style: number;
    recent: number;
  };
  recommendations: string[];
  missingElements: string[];
}

// 上下文模板
interface ContextTemplate {
  id: string;
  name: string;
  description: string;
  category: 'chapter' | 'character' | 'dialogue' | 'analysis';

  template: {
    structure: string;
    variables: TemplateVariable[];
    defaultValues: Record<string, any>;
  };

  usage: {
    useCount: number;
    averageRating: number;
    lastUsed: Date;
  };

  createdAt: Date;
  updatedAt: Date;
}

// 上下文提取引擎
class ContextExtractionEngine {
  private relevanceCalculator = new RelevanceCalculator();
  private contentAnalyzer = new ContentAnalyzer();

  async extractContext(projectId: string, targetChapterId: string, options: ContextExtractionOptions): Promise<ExtractedContext> {
    // 获取基础数据
    const project = await this.getProjectData(projectId);
    const targetChapter = await this.getChapter(targetChapterId);

    // 确定提取范围
    const sourceChapters = this.determineSourceChapters(project.chapters, targetChapter, options.scope);

    // 提取各类信息
    const background = await this.extractBackground(project, sourceChapters, options);
    const characters = await this.extractCharacterContext(project, sourceChapters, options);
    const plot = await this.extractPlotContext(sourceChapters, options);
    const style = await this.extractStyleContext(sourceChapters, options);
    const recentDevelopments = await this.extractRecentDevelopments(sourceChapters, options);

    // 计算相关性评分
    const relevanceScores = await this.calculateRelevanceScores(
      { background, characters, plot, style, recentDevelopments },
      targetChapter,
      options
    );

    // 生成引用信息
    const references = this.generateReferences(sourceChapters, project);

    // 构建上下文对象
    const context: ExtractedContext = {
      id: this.generateId(),
      projectId,
      targetChapterId,
      extractionDate: new Date(),
      content: {
        background,
        characters,
        plot,
        style,
        recentDevelopments
      },
      metadata: {
        sourceChapters: sourceChapters.map(ch => ch.id),
        relevanceScores,
        extractionMethod: 'comprehensive',
        confidence: this.calculateConfidence(relevanceScores),
        totalTokens: this.estimateTokens({ background, characters, plot, style, recentDevelopments })
      },
      references
    };

    return context;
  }

  private determineSourceChapters(allChapters: any[], targetChapter: any, scope: any): any[] {
    const targetIndex = allChapters.findIndex(ch => ch.id === targetChapter.id);
    const startIndex = Math.max(0, targetIndex - scope.includePreviousChapters);
    const endIndex = Math.min(allChapters.length - 1, targetIndex + scope.includeFollowingChapters);

    return allChapters.slice(startIndex, endIndex + 1);
  }

  private async extractBackground(project: any, chapters: any[], options: any): Promise<any> {
    // 提取世界观设定
    const worldSetting = options.scope.includeWorldBuilding ? project.worldBuilding : '';

    // 从章节中提取时间地点信息
    const timeAndPlace = this.extractTimeAndPlace(chapters);

    // 提取社会背景
    const socialContext = this.extractSocialContext(chapters);

    // 提取前置事件
    const previousEvents = this.extractPreviousEvents(chapters);

    return {
      worldSetting,
      timeAndPlace,
      socialContext,
      previousEvents
    };
  }

  private async extractCharacterContext(project: any, chapters: any[], options: any): Promise<any> {
    const allCharacters = project.characters;
    const mainCharacters: ContextualCharacter[] = [];
    const supportingCharacters: ContextualCharacter[] = [];

    for (const character of allCharacters) {
      const contextualChar = await this.buildContextualCharacter(character, chapters);

      if (character.category === 'protagonist' || character.category === 'major') {
        mainCharacters.push(contextualChar);
      } else {
        supportingCharacters.push(contextualChar);
      }
    }

    // 提取角色关系
    const relationships = await this.extractCharacterRelationships(allCharacters, chapters);

    return {
      mainCharacters,
      supportingCharacters,
      relationships
    };
  }

  private async buildContextualCharacter(character: any, chapters: any[]): Promise<ContextualCharacter> {
    // 分析角色在最近章节中的状态和行为
    const recentMentions = this.findCharacterMentions(character.id, chapters);
    const currentState = this.analyzeCurrentState(character, recentMentions);
    const recentActions = this.extractRecentActions(character, recentMentions);
    const emotionalState = this.analyzeEmotionalState(character, recentMentions);

    return {
      characterId: character.id,
      name: character.name,
      currentState,
      recentActions,
      emotionalState,
      goals: character.goals || [],
      relationships: character.relationships.map((r: any) => r.description),
      relevanceScore: this.calculateCharacterRelevance(character, recentMentions)
    };
  }

  private async extractPlotContext(chapters: any[], options: any): Promise<any> {
    // 分析当前情况
    const currentSituation = this.analyzeCurrentSituation(chapters);

    // 识别进行中的冲突
    const ongoingConflicts = this.identifyOngoingConflicts(chapters);

    // 找出未解决的情节线
    const unresolvedPlotlines = this.findUnresolvedPlotlines(chapters);

    // 提取伏笔
    const foreshadowing = this.extractForeshadowing(chapters);

    return {
      currentSituation,
      ongoingConflicts,
      unresolvedPlotlines,
      foreshadowing
    };
  }

  private async extractStyleContext(chapters: any[], options: any): Promise<any> {
    // 分析叙述声音
    const narrativeVoice = this.analyzeNarrativeVoice(chapters);

    // 分析写作风格
    const writingStyle = this.analyzeWritingStyle(chapters);

    // 分析语调和情绪
    const toneAndMood = this.analyzeToneAndMood(chapters);

    // 分析节奏
    const pacing = this.analyzePacing(chapters);

    return {
      narrativeVoice,
      writingStyle,
      toneAndMood,
      pacing
    };
  }

  private async extractRecentDevelopments(chapters: any[], options: any): Promise<any> {
    const lastChapter = chapters[chapters.length - 1];

    return {
      lastChapterSummary: lastChapter?.summary || '',
      characterChanges: this.identifyCharacterChanges(chapters),
      plotProgression: this.identifyPlotProgression(chapters),
      newElements: this.identifyNewElements(chapters)
    };
  }

  // 辅助方法实现
  private extractTimeAndPlace(chapters: any[]): string {
    // 实现时间地点提取逻辑
    return '';
  }

  private extractSocialContext(chapters: any[]): string {
    // 实现社会背景提取逻辑
    return '';
  }

  private extractPreviousEvents(chapters: any[]): string {
    // 实现前置事件提取逻辑
    return '';
  }

  private findCharacterMentions(characterId: string, chapters: any[]): any[] {
    // 查找角色在章节中的提及
    return [];
  }

  private analyzeCurrentState(character: any, mentions: any[]): string {
    // 分析角色当前状态
    return '';
  }

  private extractRecentActions(character: any, mentions: any[]): string[] {
    // 提取角色最近的行为
    return [];
  }

  private analyzeEmotionalState(character: any, mentions: any[]): string {
    // 分析角色情感状态
    return '';
  }

  private calculateCharacterRelevance(character: any, mentions: any[]): number {
    // 计算角色相关性
    return 0.5;
  }

  private extractCharacterRelationships(characters: any[], chapters: any[]): CharacterRelationshipContext[] {
    // 提取角色关系上下文
    return [];
  }

  private analyzeCurrentSituation(chapters: any[]): string {
    // 分析当前情况
    return '';
  }

  private identifyOngoingConflicts(chapters: any[]): string[] {
    // 识别进行中的冲突
    return [];
  }

  private findUnresolvedPlotlines(chapters: any[]): string[] {
    // 找出未解决的情节线
    return [];
  }

  private extractForeshadowing(chapters: any[]): string[] {
    // 提取伏笔
    return [];
  }

  private analyzeNarrativeVoice(chapters: any[]): string {
    // 分析叙述声音
    return '';
  }

  private analyzeWritingStyle(chapters: any[]): string {
    // 分析写作风格
    return '';
  }

  private analyzeToneAndMood(chapters: any[]): string {
    // 分析语调和情绪
    return '';
  }

  private analyzePacing(chapters: any[]): string {
    // 分析节奏
    return '';
  }

  private identifyCharacterChanges(chapters: any[]): string[] {
    // 识别角色变化
    return [];
  }

  private identifyPlotProgression(chapters: any[]): string[] {
    // 识别情节发展
    return [];
  }

  private identifyNewElements(chapters: any[]): string[] {
    // 识别新元素
    return [];
  }

  private async calculateRelevanceScores(content: any, targetChapter: any, options: any): Promise<Record<string, number>> {
    // 计算相关性评分
    return {};
  }

  private generateReferences(chapters: any[], project: any): ContextReference[] {
    // 生成引用信息
    return [];
  }

  private calculateConfidence(relevanceScores: Record<string, number>): number {
    // 计算置信度
    return 0.8;
  }

  private estimateTokens(content: any): number {
    // 估算token数量
    return 1000;
  }

  private async getProjectData(projectId: string): Promise<any> {
    // 获取项目数据
    return {};
  }

  private async getChapter(chapterId: string): Promise<any> {
    // 获取章节数据
    return {};
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

// 上下文优化器
class ContextOptimizer {
  async optimizeContext(context: ExtractedContext, constraints: ContextConstraints): Promise<OptimizedContext> {
    const originalTokens = context.metadata.totalTokens;

    // 如果已经在限制范围内，直接返回
    if (originalTokens <= constraints.maxTokens) {
      return this.createOptimizedContext(context, context.content, [], originalTokens, originalTokens);
    }

    // 应用优化策略
    const strategies: OptimizationStrategy[] = [];
    let optimizedContent = JSON.parse(JSON.stringify(context.content));
    let currentTokens = originalTokens;

    // 策略1: 移除低相关性内容
    if (currentTokens > constraints.maxTokens) {
      const strategy = await this.removeLowRelevanceContent(optimizedContent, context.metadata.relevanceScores);
      strategies.push(strategy);
      currentTokens -= strategy.tokensReduced;
    }

    // 策略2: 压缩描述性内容
    if (currentTokens > constraints.maxTokens) {
      const strategy = await this.compressDescriptiveContent(optimizedContent);
      strategies.push(strategy);
      currentTokens -= strategy.tokensReduced;
    }

    // 策略3: 合并相似内容
    if (currentTokens > constraints.maxTokens) {
      const strategy = await this.mergeSimilarContent(optimizedContent);
      strategies.push(strategy);
      currentTokens -= strategy.tokensReduced;
    }

    // 策略4: 使用摘要替换详细内容
    if (currentTokens > constraints.maxTokens) {
      const strategy = await this.summarizeDetailedContent(optimizedContent);
      strategies.push(strategy);
      currentTokens -= strategy.tokensReduced;
    }

    return this.createOptimizedContext(context, optimizedContent, strategies, originalTokens, currentTokens);
  }

  private async removeLowRelevanceContent(content: any, relevanceScores: Record<string, number>): Promise<OptimizationStrategy> {
    // 移除低相关性内容的实现
    return {
      name: '移除低相关性内容',
      description: '移除相关性评分低于阈值的内容',
      impact: 'medium',
      tokensReduced: 200,
      informationLoss: 0.1
    };
  }

  private async compressDescriptiveContent(content: any): Promise<OptimizationStrategy> {
    // 压缩描述性内容的实现
    return {
      name: '压缩描述性内容',
      description: '简化冗长的描述性文本',
      impact: 'low',
      tokensReduced: 150,
      informationLoss: 0.05
    };
  }

  private async mergeSimilarContent(content: any): Promise<OptimizationStrategy> {
    // 合并相似内容的实现
    return {
      name: '合并相似内容',
      description: '将相似的信息合并为单一描述',
      impact: 'medium',
      tokensReduced: 100,
      informationLoss: 0.02
    };
  }

  private async summarizeDetailedContent(content: any): Promise<OptimizationStrategy> {
    // 使用摘要替换详细内容的实现
    return {
      name: '摘要化详细内容',
      description: '用简洁摘要替换详细描述',
      impact: 'high',
      tokensReduced: 300,
      informationLoss: 0.15
    };
  }

  private createOptimizedContext(
    originalContext: ExtractedContext,
    optimizedContent: any,
    strategies: OptimizationStrategy[],
    originalTokens: number,
    optimizedTokens: number
  ): OptimizedContext {
    const compressionRatio = optimizedTokens / originalTokens;
    const totalInformationLoss = strategies.reduce((sum, s) => sum + s.informationLoss, 0);
    const retainedInformation = Math.max(0, 1 - totalInformationLoss);

    return {
      originalContext,
      optimizedContent: JSON.stringify(optimizedContent),
      optimization: {
        originalTokens,
        optimizedTokens,
        compressionRatio,
        retainedInformation
      },
      appliedStrategies: strategies,
      quality: {
        coherence: Math.max(0.5, 1 - totalInformationLoss * 0.5),
        completeness: retainedInformation,
        relevance: Math.min(1, 0.8 + (1 - compressionRatio) * 0.2),
        clarity: Math.max(0.6, 1 - totalInformationLoss * 0.3)
      }
    };
  }
}

// 相关性计算器
class RelevanceCalculator {
  calculateRelevance(content: string, target: string, context: any): number {
    // 实现相关性计算算法
    // 可以使用TF-IDF、余弦相似度、语义相似度等方法
    return 0.5;
  }
}

// 内容分析器
class ContentAnalyzer {
  analyzeContent(content: string): any {
    // 实现内容分析
    return {};
  }
}
```

### 5.17 网络小说平台适配模块

#### 5.17.1 功能概述
网络小说平台适配模块根据用户选择的目标平台，自动调整生成内容的风格、长度和特色，确保内容符合不同平台的读者偏好和平台要求。

#### 5.17.2 核心功能
- **平台选择**: 支持起点、番茄、晋江、17K、纵横、七猫、飞卢等主流平台
- **内容适配**: 根据平台特色自动调整章节长度、语言风格、情节节奏
- **风格优化**: 针对不同平台读者群体优化表达方式和内容重点
- **格式调整**: 自动调整段落结构、对话格式等符合平台规范
- **特色元素**: 添加平台特有的流行元素和套路

#### 5.17.5 技术实现
```typescript
// 平台适配服务接口
interface PlatformAdaptationService {
  // 获取支持的平台列表
  getSupportedPlatforms(): Promise<PlatformInfo[]>;

  // 获取平台配置
  getPlatformConfig(platformId: string): Promise<PlatformConfig>;

  // 更新平台配置
  updatePlatformConfig(platformId: string, config: Partial<PlatformConfig>): Promise<void>;

  // 适配内容到指定平台
  adaptContentToPlatform(content: string, platformId: string, options: AdaptationOptions): Promise<AdaptedContent>;

  // 批量适配
  batchAdaptContent(contents: ContentInput[], platformId: string, options: AdaptationOptions): Promise<AdaptedContent[]>;

  // 分析内容与平台的匹配度
  analyzeContentFit(content: string, platformId: string): Promise<ContentFitAnalysis>;

  // 生成平台特定的标题建议
  generatePlatformTitles(content: string, platformId: string, count: number): Promise<string[]>;

  // 获取平台写作建议
  getPlatformWritingTips(platformId: string): Promise<WritingTips>;

  // 预览适配效果
  previewAdaptation(content: string, platformId: string, options: AdaptationOptions): Promise<AdaptationPreview>;
}

// 平台信息
interface PlatformInfo {
  id: string;
  name: string;
  displayName: string;
  description: string;
  logo: string;
  category: PlatformCategory;
  popularity: number;
  isActive: boolean;
  supportedGenres: string[];
  lastUpdated: Date;
}

enum PlatformCategory {
  MAINSTREAM = 'mainstream',     // 主流平台
  NICHE = 'niche',              // 小众平台
  INTERNATIONAL = 'international', // 国际平台
  MOBILE_FIRST = 'mobile_first'  // 移动优先平台
}

// 平台配置
interface PlatformConfig {
  id: string;
  name: string;

  // 内容要求
  contentRequirements: {
    chapterLength: {
      min: number;
      max: number;
      recommended: number;
      unit: 'characters' | 'words';
    };

    languageStyle: LanguageStyle;
    paceRequirement: PaceRequirement;

    // 格式要求
    formatting: {
      paragraphLength: { min: number; max: number };
      dialogueFormat: 'quotes' | 'dashes' | 'mixed';
      punctuationStyle: 'traditional' | 'modern' | 'internet';
      lineBreakStyle: 'single' | 'double' | 'paragraph';
    };

    // 内容限制
    restrictions: {
      prohibitedContent: string[];
      sensitiveTopics: string[];
      requiredWarnings: string[];
    };
  };

  // 读者偏好
  readerPreferences: {
    preferredGenres: GenrePreference[];
    popularTropes: TropeInfo[];
    avoidedElements: string[];

    // 阅读习惯
    readingHabits: {
      preferredReadingTime: 'morning' | 'afternoon' | 'evening' | 'night';
      attentionSpan: 'short' | 'medium' | 'long';
      devicePreference: 'mobile' | 'desktop' | 'tablet';
    };
  };

  // 生成策略
  generationStrategy: {
    suspenseLevel: SuspenseLevel;
    conflictDensity: number;
    characterFocus: CharacterFocus;

    // 平台特色
    platformFeatures: {
      usePopularPhrases: boolean;
      includeInternetSlang: boolean;
      emphasizeEmotions: boolean;
      addReaderEngagement: boolean;
    };

    // 优化目标
    optimizationTargets: {
      retention: number;      // 读者留存权重
      engagement: number;     // 互动参与权重
      virality: number;       // 传播性权重
      monetization: number;   // 商业化权重
    };
  };

  // 发布规范
  publishingGuidelines: {
    updateFrequency: {
      recommended: 'daily' | 'every_other_day' | 'weekly' | 'flexible';
      minimumInterval: number; // 小时
    };

    titleGuidelines: {
      maxLength: number;
      recommendedKeywords: string[];
      avoidedWords: string[];
      useNumbers: boolean;
      useEmojis: boolean;
    };

    tagsAndCategories: {
      maxTags: number;
      requiredTags: string[];
      popularTags: string[];
      categoryMapping: Record<string, string>;
    };
  };

  // 算法偏好
  algorithmPreferences: {
    keywordDensity: number;
    readingTimeOptimal: number; // 分钟
    engagementSignals: string[];
    rankingFactors: RankingFactor[];
  };
}

enum LanguageStyle {
  FORMAL = 'formal',
  CASUAL = 'casual',
  INTERNET = 'internet',
  LITERARY = 'literary',
  CONVERSATIONAL = 'conversational'
}

enum PaceRequirement {
  VERY_FAST = 'very_fast',
  FAST = 'fast',
  MEDIUM = 'medium',
  SLOW = 'slow',
  VARIABLE = 'variable'
}

enum SuspenseLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  EXTREME = 'extreme'
}

enum CharacterFocus {
  PROTAGONIST = 'protagonist',
  ENSEMBLE = 'ensemble',
  RELATIONSHIP = 'relationship',
  ANTAGONIST = 'antagonist'
}

// 类型偏好
interface GenrePreference {
  genre: string;
  popularity: number;
  characteristics: string[];
  successFactors: string[];
}

// 套路信息
interface TropeInfo {
  name: string;
  description: string;
  popularity: number;
  usage: 'frequent' | 'moderate' | 'occasional';
  examples: string[];
  variations: string[];
}

// 排名因素
interface RankingFactor {
  factor: string;
  weight: number;
  description: string;
  optimization: string;
}

// 适配选项
interface AdaptationOptions {
  targetPlatform: string;
  adaptationLevel: 'light' | 'moderate' | 'aggressive';
  preserveOriginalStyle: boolean;

  // 具体适配项
  adaptations: {
    adjustLength: boolean;
    modifyLanguageStyle: boolean;
    optimizePacing: boolean;
    enhanceSuspense: boolean;
    addPlatformElements: boolean;
    adjustFormatting: boolean;
  };

  // 自定义规则
  customRules: CustomAdaptationRule[];

  // 质量控制
  qualityControl: {
    preserveMeaning: boolean;
    maintainCharacterConsistency: boolean;
    keepPlotIntegrity: boolean;
    minimumQualityScore: number;
  };
}

// 内容输入
interface ContentInput {
  id: string;
  title: string;
  content: string;
  type: 'chapter' | 'synopsis' | 'character_description';
  metadata: {
    genre: string;
    tags: string[];
    targetAudience: string;
    originalPlatform?: string;
  };
}

// 适配后的内容
interface AdaptedContent {
  originalContent: ContentInput;
  adaptedContent: string;
  adaptedTitle?: string;

  // 适配统计
  adaptationStats: {
    originalLength: number;
    adaptedLength: number;
    lengthChangeRatio: number;
    modificationsCount: number;
    adaptationTime: number;
  };

  // 适配详情
  modifications: ContentModification[];

  // 质量评估
  qualityAssessment: {
    platformFitScore: number;
    readabilityScore: number;
    engagementPotential: number;
    overallQuality: number;
  };

  // 建议
  recommendations: string[];
  warnings: string[];
}

// 内容修改
interface ContentModification {
  type: ModificationType;
  position: { start: number; end: number };
  originalText: string;
  modifiedText: string;
  reason: string;
  confidence: number;
}

enum ModificationType {
  LENGTH_ADJUSTMENT = 'length_adjustment',
  LANGUAGE_STYLE = 'language_style',
  PACE_OPTIMIZATION = 'pace_optimization',
  SUSPENSE_ENHANCEMENT = 'suspense_enhancement',
  PLATFORM_ELEMENT = 'platform_element',
  FORMATTING = 'formatting',
  CULTURAL_ADAPTATION = 'cultural_adaptation'
}

// 内容匹配度分析
interface ContentFitAnalysis {
  overallFitScore: number;

  // 分项评分
  categoryScores: {
    lengthFit: number;
    styleFit: number;
    paceFit: number;
    genreFit: number;
    audienceFit: number;
  };

  // 详细分析
  analysis: {
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    threats: string[];
  };

  // 改进建议
  improvements: ImprovementSuggestion[];

  // 竞争分析
  competitiveAnalysis: {
    similarWorks: string[];
    marketPosition: string;
    differentiationPoints: string[];
  };
}

// 改进建议
interface ImprovementSuggestion {
  category: string;
  priority: 'high' | 'medium' | 'low';
  suggestion: string;
  expectedImpact: string;
  implementationDifficulty: 'easy' | 'medium' | 'hard';
  examples: string[];
}

// 写作技巧
interface WritingTips {
  platformId: string;

  // 通用技巧
  generalTips: {
    category: string;
    tips: WritingTip[];
  }[];

  // 特定技巧
  specificTips: {
    genre: string;
    tips: WritingTip[];
  }[];

  // 成功案例
  successCases: SuccessCase[];

  // 常见错误
  commonMistakes: CommonMistake[];
}

interface WritingTip {
  title: string;
  description: string;
  example: string;
  importance: 'high' | 'medium' | 'low';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

interface SuccessCase {
  title: string;
  author: string;
  genre: string;
  successFactors: string[];
  keyTechniques: string[];
  metrics: {
    readership: number;
    rating: number;
    engagement: number;
  };
}

interface CommonMistake {
  mistake: string;
  description: string;
  consequences: string[];
  solutions: string[];
  examples: {
    wrong: string;
    correct: string;
  };
}

// 适配预览
interface AdaptationPreview {
  originalContent: string;
  adaptedContent: string;

  // 对比信息
  comparison: {
    lengthComparison: {
      original: number;
      adapted: number;
      change: number;
      changePercentage: number;
    };

    styleComparison: {
      originalStyle: string;
      adaptedStyle: string;
      keyChanges: string[];
    };

    structureComparison: {
      originalStructure: string[];
      adaptedStructure: string[];
      modifications: string[];
    };
  };

  // 预期效果
  expectedOutcomes: {
    readerEngagement: number;
    platformAlgorithmScore: number;
    commercialPotential: number;
    viralPotential: number;
  };
}

// 自定义适配规则
interface CustomAdaptationRule {
  id: string;
  name: string;
  description: string;

  // 触发条件
  trigger: {
    contentPattern: string;
    contextConditions: string[];
    platformSpecific: boolean;
  };

  // 适配动作
  action: {
    type: 'replace' | 'insert' | 'delete' | 'modify';
    target: string;
    replacement?: string;
    modification?: string;
  };

  // 规则配置
  config: {
    priority: number;
    enabled: boolean;
    applyToGenres: string[];
    excludeFromGenres: string[];
  };
}

// 平台适配引擎
class PlatformAdaptationEngine {
  private platformConfigs: Map<string, PlatformConfig> = new Map();
  private adaptationRules: Map<string, CustomAdaptationRule[]> = new Map();

  constructor() {
    this.loadPlatformConfigs();
    this.loadAdaptationRules();
  }

  async adaptContentToPlatform(content: string, platformId: string, options: AdaptationOptions): Promise<AdaptedContent> {
    const platformConfig = this.platformConfigs.get(platformId);
    if (!platformConfig) {
      throw new Error(`不支持的平台: ${platformId}`);
    }

    const modifications: ContentModification[] = [];
    let adaptedContent = content;

    // 应用各种适配策略
    if (options.adaptations.adjustLength) {
      const lengthResult = await this.adjustContentLength(adaptedContent, platformConfig);
      adaptedContent = lengthResult.content;
      modifications.push(...lengthResult.modifications);
    }

    if (options.adaptations.modifyLanguageStyle) {
      const styleResult = await this.adaptLanguageStyle(adaptedContent, platformConfig);
      adaptedContent = styleResult.content;
      modifications.push(...styleResult.modifications);
    }

    if (options.adaptations.optimizePacing) {
      const paceResult = await this.optimizePacing(adaptedContent, platformConfig);
      adaptedContent = paceResult.content;
      modifications.push(...paceResult.modifications);
    }

    if (options.adaptations.enhanceSuspense) {
      const suspenseResult = await this.enhanceSuspense(adaptedContent, platformConfig);
      adaptedContent = suspenseResult.content;
      modifications.push(...suspenseResult.modifications);
    }

    if (options.adaptations.addPlatformElements) {
      const elementsResult = await this.addPlatformElements(adaptedContent, platformConfig);
      adaptedContent = elementsResult.content;
      modifications.push(...elementsResult.modifications);
    }

    // 应用自定义规则
    const customResult = await this.applyCustomRules(adaptedContent, platformId, options.customRules);
    adaptedContent = customResult.content;
    modifications.push(...customResult.modifications);

    // 质量评估
    const qualityAssessment = await this.assessQuality(adaptedContent, platformConfig);

    // 生成建议和警告
    const recommendations = this.generateRecommendations(modifications, qualityAssessment);
    const warnings = this.generateWarnings(modifications, qualityAssessment);

    return {
      originalContent: {
        id: '',
        title: '',
        content,
        type: 'chapter',
        metadata: { genre: '', tags: [], targetAudience: '' }
      },
      adaptedContent,
      adaptationStats: {
        originalLength: content.length,
        adaptedLength: adaptedContent.length,
        lengthChangeRatio: adaptedContent.length / content.length,
        modificationsCount: modifications.length,
        adaptationTime: Date.now()
      },
      modifications,
      qualityAssessment,
      recommendations,
      warnings
    };
  }

  private async adjustContentLength(content: string, config: PlatformConfig): Promise<{
    content: string;
    modifications: ContentModification[];
  }> {
    const targetLength = config.contentRequirements.chapterLength.recommended;
    const currentLength = content.length;
    const modifications: ContentModification[] = [];

    if (Math.abs(currentLength - targetLength) / targetLength < 0.1) {
      // 长度已经合适，不需要调整
      return { content, modifications };
    }

    let adjustedContent = content;

    if (currentLength > targetLength * 1.2) {
      // 内容过长，需要压缩
      adjustedContent = await this.compressContent(content, targetLength);
      modifications.push({
        type: ModificationType.LENGTH_ADJUSTMENT,
        position: { start: 0, end: content.length },
        originalText: content,
        modifiedText: adjustedContent,
        reason: '内容过长，进行压缩以符合平台要求',
        confidence: 0.8
      });
    } else if (currentLength < targetLength * 0.8) {
      // 内容过短，需要扩展
      adjustedContent = await this.expandContent(content, targetLength);
      modifications.push({
        type: ModificationType.LENGTH_ADJUSTMENT,
        position: { start: 0, end: content.length },
        originalText: content,
        modifiedText: adjustedContent,
        reason: '内容过短，进行扩展以符合平台要求',
        confidence: 0.7
      });
    }

    return { content: adjustedContent, modifications };
  }

  private async adaptLanguageStyle(content: string, config: PlatformConfig): Promise<{
    content: string;
    modifications: ContentModification[];
  }> {
    const targetStyle = config.contentRequirements.languageStyle;
    const modifications: ContentModification[] = [];

    // 根据目标风格调整语言
    let adaptedContent = content;

    switch (targetStyle) {
      case LanguageStyle.INTERNET:
        adaptedContent = await this.convertToInternetStyle(content);
        break;
      case LanguageStyle.CASUAL:
        adaptedContent = await this.convertToCasualStyle(content);
        break;
      case LanguageStyle.FORMAL:
        adaptedContent = await this.convertToFormalStyle(content);
        break;
      // 其他风格...
    }

    if (adaptedContent !== content) {
      modifications.push({
        type: ModificationType.LANGUAGE_STYLE,
        position: { start: 0, end: content.length },
        originalText: content,
        modifiedText: adaptedContent,
        reason: `调整语言风格为${targetStyle}`,
        confidence: 0.75
      });
    }

    return { content: adaptedContent, modifications };
  }

  private async optimizePacing(content: string, config: PlatformConfig): Promise<{
    content: string;
    modifications: ContentModification[];
  }> {
    // 实现节奏优化逻辑
    return { content, modifications: [] };
  }

  private async enhanceSuspense(content: string, config: PlatformConfig): Promise<{
    content: string;
    modifications: ContentModification[];
  }> {
    // 实现悬念增强逻辑
    return { content, modifications: [] };
  }

  private async addPlatformElements(content: string, config: PlatformConfig): Promise<{
    content: string;
    modifications: ContentModification[];
  }> {
    // 实现平台特色元素添加逻辑
    return { content, modifications: [] };
  }

  private async applyCustomRules(content: string, platformId: string, rules: CustomAdaptationRule[]): Promise<{
    content: string;
    modifications: ContentModification[];
  }> {
    // 实现自定义规则应用逻辑
    return { content, modifications: [] };
  }

  private async assessQuality(content: string, config: PlatformConfig): Promise<any> {
    // 实现质量评估逻辑
    return {
      platformFitScore: 0.8,
      readabilityScore: 0.85,
      engagementPotential: 0.75,
      overallQuality: 0.8
    };
  }

  private generateRecommendations(modifications: ContentModification[], quality: any): string[] {
    // 生成改进建议
    return [];
  }

  private generateWarnings(modifications: ContentModification[], quality: any): string[] {
    // 生成警告信息
    return [];
  }

  // 辅助方法
  private async compressContent(content: string, targetLength: number): Promise<string> {
    // 实现内容压缩逻辑
    return content.substring(0, targetLength);
  }

  private async expandContent(content: string, targetLength: number): Promise<string> {
    // 实现内容扩展逻辑
    return content;
  }

  private async convertToInternetStyle(content: string): Promise<string> {
    // 转换为网络化风格
    return content;
  }

  private async convertToCasualStyle(content: string): Promise<string> {
    // 转换为随意风格
    return content;
  }

  private async convertToFormalStyle(content: string): Promise<string> {
    // 转换为正式风格
    return content;
  }

  private loadPlatformConfigs(): void {
    // 加载平台配置
    // 实际实现中从配置文件或数据库加载
  }

  private loadAdaptationRules(): void {
    // 加载适配规则
    // 实际实现中从配置文件或数据库加载
  }
}

// 平台分析器
class PlatformAnalyzer {
  async analyzeContentFit(content: string, platformId: string): Promise<ContentFitAnalysis> {
    // 实现内容匹配度分析
    return {
      overallFitScore: 0.8,
      categoryScores: {
        lengthFit: 0.9,
        styleFit: 0.7,
        paceFit: 0.8,
        genreFit: 0.85,
        audienceFit: 0.75
      },
      analysis: {
        strengths: ['节奏控制良好', '语言风格适合'],
        weaknesses: ['章节长度偏短', '悬念设置不足'],
        opportunities: ['可以增加互动元素', '优化开头吸引力'],
        threats: ['竞争激烈', '读者注意力分散']
      },
      improvements: [],
      competitiveAnalysis: {
        similarWorks: [],
        marketPosition: '中等',
        differentiationPoints: []
      }
    };
  }
}
```

## 6. 创作流程设计

### 6.1 完整创作流程

AI小说助手提供从构思到发布的完整创作流程，帮助作者高效创作高质量的网络小说。

#### 6.1.1 创作流程图
```mermaid
flowchart TD
    A[项目创建] --> B[大纲生成]
    B --> C[大纲编辑]
    C --> D[人物设计]
    D --> E[章节创作]
    E --> F[章节分析]
    F --> G{需要优化?}
    G -->|是| H[章节润色]
    G -->|否| I[继续创作]
    H --> I
    I --> J{全书完成?}
    J -->|否| E
    J -->|是| K[统计分析]
    K --> L[导出发布]
```

#### 6.1.2 详细创作步骤

**第一步：大纲生成**
1. 在"大纲生成"模块中填写小说基本信息
   - 小说标题、类型、主题、风格
   - 章节数量和每章字数设置
   - 人物数量配置（主角、重要角色、配角、反派、龙套）
2. 选择生成范围（起始章节到结束章节）
3. 选择AI模型并点击"生成大纲"
4. 系统自动生成包含以下内容的完整大纲：
   - 小说标题和核心主题
   - 主要人物设定
   - 故事梗概
   - 章节结构
   - 世界观设定

**第二步：大纲编辑**
1. 在"大纲编辑"模块中完善AI生成的大纲
2. 可使用AI辅助功能优化以下内容：
   - 小说标题优化
   - 中心思想完善
   - 故事梗概扩展
   - 世界观设定细化
3. 保存修改并确认大纲结构

**第三步：人物设计**
1. 在"人物编辑"模块中管理角色信息
2. 为每个角色设置详细信息：
   - 基本信息（姓名、身份、职业）
   - 外貌描述
   - 性格特点
   - 背景故事
3. 使用"人物关系图"建立角色间的关系网络
4. 可使用AI辅助生成丰富的角色设定

**第四步：章节创作**
1. 在"章节生成"模块中选择要创作的章节
2. 系统自动加载相关上下文信息：
   - 前序章节内容
   - 相关角色信息
   - 大纲要求
3. 设置创作参数：
   - 目标字数
   - 写作风格
   - 关联角色
4. 使用AI生成章节内容
5. 使用编辑工具进行润色和修改：
   - 选中文本润色
   - 段落扩写
   - 内容改写
   - 降AI味处理

**第五步：章节分析与润色**
1. 在"章节分析"模块中选择已完成的章节
2. 选择分析维度：
   - 核心剧情分析
   - 故事梗概提取
   - 优缺点分析
   - 角色标注
   - 物品标注
   - 改进建议
3. 查看AI分析结果
4. 根据分析建议使用"章节改进"功能自动优化内容

**第六步：质量控制**
1. 使用"降AI味"功能优化AI生成的内容
2. 检查内容的自然度和可读性
3. 确保角色行为和语言的一致性
4. 验证剧情逻辑的合理性

**第七步：进度管理**
1. 在"统计信息"模块查看创作进度
2. 监控以下指标：
   - 总字数和完成度
   - 章节完成情况
   - 创作趋势分析
   - 日均产出统计
3. 根据统计数据调整创作计划

**第八步：保存和导出**
1. 使用工具栏的"保存"功能保存项目（.ainovel格式）
2. 可选择导出格式：
   - 纯文本格式（.txt）
   - Word文档格式（.docx）
   - Markdown格式（.md）
3. 支持分章节导出或整本导出

#### 6.1.3 .ainovel项目格式说明
```json
{
  "version": "1.0",
  "projectInfo": {
    "title": "小说标题",
    "author": "作者名称",
    "genre": "小说类型",
    "theme": "小说主题",
    "style": "写作风格",
    "totalChapters": 50,
    "wordsPerChapter": 3000,
    "createdAt": "2024-01-29T10:00:00Z",
    "updatedAt": "2024-01-29T15:30:00Z"
  },
  "outline": {
    "summary": "故事梗概",
    "worldview": "世界观设定",
    "chapters": [
      {
        "number": 1,
        "title": "第一章：开端",
        "summary": "章节简介",
        "content": "章节内容",
        "wordCount": 3200,
        "status": "completed",
        "createdAt": "2024-01-29T10:30:00Z"
      }
    ]
  },
  "characters": [
    {
      "id": "char_001",
      "name": "主角姓名",
      "roleType": "主角",
      "personality": "性格特点",
      "background": "背景故事",
      "appearance": "外貌描述"
    }
  ],
  "relationships": [
    {
      "character1": "char_001",
      "character2": "char_002",
      "type": "朋友",
      "description": "关系描述"
    }
  ],
  "settings": {
    "aiModels": "使用的AI模型配置",
    "templates": "自定义模板",
    "preferences": "用户偏好设置"
  }
}
```

### 6.2 创作技巧和最佳实践

#### 6.2.1 大纲创作技巧
- **明确主题**: 确定小说的核心主题和价值观
- **角色驱动**: 以角色为中心构建故事情节
- **冲突设计**: 设置多层次的冲突推动情节发展
- **节奏控制**: 合理安排高潮和低潮的节奏

#### 6.2.2 章节创作技巧
- **开头吸引**: 每章开头要有吸引读者的元素
- **结尾悬念**: 适当的悬念保持读者兴趣
- **对话生动**: 通过对话展现角色性格
- **环境描写**: 适度的环境描写增强代入感

#### 6.2.3 AI辅助使用技巧
- **上下文管理**: 确保AI了解完整的故事背景
- **提示词优化**: 使用具体明确的提示词
- **分段生成**: 将长章节分段生成以保证质量
- **人工润色**: AI生成后必须进行人工润色

### 6.3 网络小说平台适配功能

#### 6.3.1 平台适配系统设计

**功能概述**
根据用户选择的小说类型、小说主题等来生成相对应的大纲、章节结构和剧情等，生成符合现在的网络小说读者（如番茄小说、飞卢、17K、起点、纵横、晋江、七猫等小说群体）的内容。

**平台配置数据结构**
```typescript
interface PlatformConfig {
  id: string;
  name: string;
  description: string;

  // 内容要求
  contentRequirements: {
    chapterLength: { min: number; max: number; recommended: number };
    updateFrequency: string;
    languageStyle: string;
    paceRequirement: 'fast' | 'medium' | 'slow';
  };

  // 读者偏好
  readerPreferences: {
    preferredGenres: string[];
    popularTropes: string[];
    avoidedElements: string[];
    engagementTactics: string[];
  };

  // 平台特色
  platformFeatures: {
    supportedFormats: string[];
    monetizationModel: string;
    algorithmPreferences: string[];
    promotionStrategies: string[];
  };

  // 生成策略
  generationStrategy: {
    outlineTemplate: string;
    chapterTemplate: string;
    characterTemplate: string;
    plotDevicePreferences: string[];
  };
}
```

**平台适配引擎**
```typescript
class PlatformAdaptationEngine {
  private platforms: Map<string, PlatformConfig>;

  // 根据平台调整大纲生成策略
  async adaptOutlineForPlatform(
    baseOutline: NovelOutline,
    platformId: string
  ): Promise<NovelOutline> {
    const platform = this.platforms.get(platformId);
    if (!platform) return baseOutline;

    // 调整章节长度
    const adaptedOutline = this.adjustChapterLength(baseOutline, platform);

    // 调整情节节奏
    const pacedOutline = this.adjustPlotPacing(adaptedOutline, platform);

    // 添加平台特色元素
    const enhancedOutline = this.addPlatformElements(pacedOutline, platform);

    return enhancedOutline;
  }

  // 根据平台调整章节生成
  async adaptChapterForPlatform(
    chapterContent: string,
    platformId: string,
    chapterNumber: number
  ): Promise<string> {
    const platform = this.platforms.get(platformId);
    if (!platform) return chapterContent;

    // 调整语言风格
    let adaptedContent = this.adjustLanguageStyle(chapterContent, platform);

    // 优化悬念设置
    adaptedContent = this.optimizeSuspense(adaptedContent, platform, chapterNumber);

    // 添加平台特色表达
    adaptedContent = this.addPlatformExpressions(adaptedContent, platform);

    return adaptedContent;
  }

  // 生成平台特定的提示词
  generatePlatformPrompt(platformId: string, contentType: string): string {
    const platform = this.platforms.get(platformId);
    if (!platform) return '';

    const basePrompt = this.getBasePrompt(contentType);
    const platformSpecific = this.getPlatformSpecificPrompt(platform, contentType);

    return `${basePrompt}\n\n平台适配要求：\n${platformSpecific}`;
  }
}
```

**内置平台配置**
```typescript
const platformConfigs: PlatformConfig[] = [
  {
    id: 'qidian',
    name: '起点中文网',
    description: '国内最大的网络文学平台，注重传统网文套路',
    contentRequirements: {
      chapterLength: { min: 3000, max: 5000, recommended: 3500 },
      updateFrequency: '日更',
      languageStyle: '网络化',
      paceRequirement: 'medium'
    },
    readerPreferences: {
      preferredGenres: ['玄幻', '都市', '历史', '科幻', '游戏'],
      popularTropes: ['升级流', '爽文', '打脸', '装逼'],
      avoidedElements: ['过度文艺', '节奏拖沓'],
      engagementTactics: ['悬念设置', '爽点密集', '冲突激烈']
    },
    platformFeatures: {
      supportedFormats: ['章节连载'],
      monetizationModel: '付费阅读',
      algorithmPreferences: ['更新稳定', '互动率高'],
      promotionStrategies: ['榜单推荐', '编辑推荐']
    },
    generationStrategy: {
      outlineTemplate: 'traditional_webnovel',
      chapterTemplate: 'suspense_ending',
      characterTemplate: 'growth_oriented',
      plotDevicePreferences: ['金手指', '系统流', '重生穿越']
    }
  },

  {
    id: 'tomato',
    name: '番茄小说',
    description: '字节跳动旗下免费阅读平台，注重短平快',
    contentRequirements: {
      chapterLength: { min: 1500, max: 3000, recommended: 2000 },
      updateFrequency: '日更多章',
      languageStyle: '通俗易懂',
      paceRequirement: 'fast'
    },
    readerPreferences: {
      preferredGenres: ['都市', '言情', '玄幻', '军事'],
      popularTropes: ['快节奏', '短篇爽文', '即时满足'],
      avoidedElements: ['复杂设定', '长篇铺垫'],
      engagementTactics: ['章章有梗', '快速反转', '密集爽点']
    },
    platformFeatures: {
      supportedFormats: ['短章节'],
      monetizationModel: '广告收入',
      algorithmPreferences: ['完读率', '停留时间'],
      promotionStrategies: ['算法推荐', '热门标签']
    },
    generationStrategy: {
      outlineTemplate: 'fast_paced',
      chapterTemplate: 'cliffhanger_heavy',
      characterTemplate: 'immediate_appeal',
      plotDevicePreferences: ['快速升级', '即时回报', '简单粗暴']
    }
  }
];
```

#### 6.3.2 平台适配界面设计

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                            平台适配设置                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│  平台选择                    │                适配预览                        │
│  ┌─────────────────────────┐ │  ┌─────────────────────────────────────────┐ │
│  │ 目标平台:               │ │  │ 平台: 起点中文网                        │ │
│  │ ○ 起点中文网           │ │  │                                         │ │
│  │ ○ 番茄小说             │ │  │ 内容要求:                               │ │
│  │ ○ 晋江文学城           │ │  │ • 章节长度: 3000-5000字                 │ │
│  │ ○ 17K小说网            │ │  │ • 更新频率: 日更                        │ │
│  │ ○ 纵横中文网           │ │  │ • 语言风格: 网络化                      │ │
│  │ ○ 七猫免费小说         │ │  │                                         │ │
│  │ ○ 飞卢小说网           │ │  │ 读者偏好:                               │ │
│  │                         │ │  │ • 热门类型: 玄幻、都市、历史            │ │
│  │ 适配选项:               │ │  │ • 流行套路: 升级流、爽文、打脸          │ │
│  │ ☑ 章节长度适配         │ │  │ • 参与策略: 悬念设置、爽点密集          │ │
│  │ ☑ 语言风格调整         │ │  │                                         │ │
│  │ ☑ 情节节奏优化         │ │  │ 生成策略:                               │ │
│  │ ☑ 悬念设置增强         │ │  │ • 大纲模板: 传统网文                    │ │
│  │ ☑ 平台特色元素         │ │  │ • 章节模板: 悬念结尾                    │ │
│  │                         │ │  │ • 角色模板: 成长导向                    │ │
│  │ [应用适配] [预览效果]   │ │  │                                         │ │
│  └─────────────────────────┘ │  │ [保存配置] [导出设置] [重置默认]         │ │
│                              │  └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 6.4 针对不同平台的优化建议

#### 6.4.1 起点中文网
- **字数要求**: 每章3000-5000字
- **更新频率**: 日更或隔日更新
- **标题技巧**: 使用吸引眼球的章节标题
- **内容特点**: 注重爽点设置和节奏控制

#### 6.4.2 番茄小说
- **短章节**: 每章1500-3000字
- **快节奏**: 情节推进要快速
- **悬念设置**: 每章结尾要有悬念
- **通俗易懂**: 语言要通俗易懂

#### 6.4.3 晋江文学城
- **情感描写**: 注重情感细腻描写
- **人物塑造**: 角色要立体丰满
- **文笔要求**: 对文笔有较高要求
- **题材特色**: 适合言情、耽美等题材

---

## 7. 详细界面布局设计

### 7.1 整体布局架构

AI小说助手采用经典的桌面应用布局，包含顶部工具栏、左侧导航菜单、主内容区域和底部状态栏。

#### 7.1.1 主界面整体布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 顶部工具栏                                                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                    主内容区域                                    │
│ 菜单     │                                                                 │
│ (200px)  │                   (动态宽度)                                    │
│         │                                                                 │
│         │                                                                 │
│         │                                                                 │
│         │                                                                 │
│         │                                                                 │
│         │                                                                 │
│         │                                                                 │
│         │                                                                 │
│         │                                                                 │
│         │                                                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 底部状态栏 (30px)                                                          │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.2 顶部工具栏设计 (高度: 50px)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [新建项目] [打开项目] [保存项目] [导出] │ [同步] [设置] │ [主题切换] [帮助] │
│                                        │              │                   │
│ 文件操作区                             │ 功能操作区   │ 系统操作区        │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.3 左侧导航菜单设计 (宽度: 200px)

```
┌─────────────────────────┐
│ 🏠 首页仪表盘           │
├─────────────────────────┤
│ 📝 创作功能             │
│   ├─ 📋 大纲生成        │
│   ├─ ✏️ 大纲编辑        │
│   ├─ 📑 章节编辑        │
│   ├─ 🖊️ 章节生成        │
│   └─ 🔍 章节分析        │
├─────────────────────────┤
│ 👥 角色管理             │
│   ├─ 👤 人物编辑        │
│   └─ 🕸️ 人物关系图      │
├─────────────────────────┤
│ 📊 数据统计             │
│   └─ 📈 统计信息        │
├─────────────────────────┤
│ 🤖 AI工具               │
│   ├─ 💬 AI聊天          │
│   ├─ 📚 提示词库        │
│   ├─ 🧠 上下文管理      │
│   └─ 🔎 向量库检索      │
├─────────────────────────┤
│ ⚙️ 系统设置             │
└─────────────────────────┘
```

### 7.4 底部状态栏设计 (高度: 30px)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 项目状态 │ 当前章节 │ AI连接状态 │ 保存状态 │ 内存使用 │ 时间显示 │ 版本信息 │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.5 首页仪表盘界面布局

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              首页仪表盘                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 快速操作区                                                                  │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ [新建项目] [打开项目] [最近项目] [导入项目]                             │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 项目概览                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 当前项目: [项目名称]                                                    │ │
│ │ 创建时间: [创建日期]                                                    │ │
│ │ 最后修改: [修改时间]                                                    │ │
│ │ 总章节数: [章节数量]                                                    │ │
│ │ 总字数: [字数统计]                                                      │ │
│ │ 完成进度: [进度条显示]                                                  │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 最近项目列表                                                               │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 项目名称        │ 类型   │ 章节数 │ 字数   │ 最后修改   │ 操作        │ │
│ │ ─────────────── │ ────── │ ────── │ ────── │ ────────── │ ─────────── │ │
│ │ [项目1]         │ [类型] │ [数量] │ [字数] │ [时间]     │ [打开][删除] │ │
│ │ [项目2]         │ [类型] │ [数量] │ [字数] │ [时间]     │ [打开][删除] │ │
│ │ [项目3]         │ [类型] │ [数量] │ [字数] │ [时间]     │ [打开][删除] │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 系统状态                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ AI服务状态: [连接状态列表]                                              │ │
│ │ 数据库状态: [正常/异常]                                                 │ │
│ │ 内存使用: [使用量/总量]                                                 │ │
│ │ 磁盘空间: [可用空间]                                                    │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 快捷功能                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ [AI聊天] [提示词库] [设置] [帮助文档] [检查更新]                        │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.6 大纲生成界面布局 (左右分区: 40% + 60%)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              大纲生成                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 功能区 (40%)                    │ 生成区 (60%)                              │
│ ┌─────────────────────────────┐ │ ┌─────────────────────────────────────────┐ │
│ │ AI模型选择                  │ │ │ 生成结果显示区                          │ │
│ │ [模型下拉选择框]            │ │ │                                         │ │
│ │                             │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ 提示词模板                  │ │ │ │ [生成的大纲内容显示区域]            │ │ │
│ │ [模板下拉选择框]            │ │ │ │                                     │ │ │
│ │ [自定义模板按钮]            │ │ │ │ [JSON格式或文本格式显示]            │ │ │
│ │                             │ │ │ │                                     │ │ │
│ │ 基本信息设置                │ │ │ │ [可滚动的内容区域]                  │ │ │
│ │ 小说标题: [输入框]          │ │ │ │                                     │ │ │
│ │ 小说类型: [下拉选择]        │ │ │ │                                     │ │ │
│ │ 小说主题: [下拉选择]        │ │ │ │                                     │ │ │
│ │ 小说风格: [下拉选择]        │ │ │ │                                     │ │ │
│ │ 章节数: [数字输入框]        │ │ │ │                                     │ │ │
│ │ 每章字数: [数字输入框]      │ │ │ │                                     │ │ │
│ │                             │ │ │ └─────────────────────────────────────┘ │ │
│ │ 人物设置                    │ │ │                                         │ │
│ │ 主角数量: [数字输入框]      │ │ │ 操作按钮区                              │ │
│ │ 重要角色: [数字输入框]      │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ 配角数量: [数字输入框]      │ │ │ │ [复制内容] [保存大纲] [导出文件]    │ │ │
│ │ 反派数量: [数字输入框]      │ │ │ │ [重新生成] [清空内容]               │ │ │
│ │ 龙套数量: [数字输入框]      │ │ │ └─────────────────────────────────────┘ │ │
│ │                             │ │ │ └─────────────────────────────────────┘ │ │
│ │ 生成范围                    │ │ │                                         │ │
│ │ 起始章: [数字输入框]        │ │ │ 生成状态显示                            │ │
│ │ 结束章: [数字输入框]        │ │ │ ┌─────────────────────────────────────┐ │ │
│ │                             │ │ │ │ 状态: [等待/生成中/完成/错误]       │ │ │
│ │ 生成控制                    │ │ │ │ 进度: [进度条显示]                  │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ 用时: [生成耗时]                    │ │ │
│ │ │ [生成大纲] [清空设置]   │ │ │ │ │ 字数: [生成字数统计]                │ │ │
│ │ └─────────────────────────┘ │ │ │ └─────────────────────────────────────┘ │ │
│ └─────────────────────────────┘ │ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.7 大纲编辑界面布局 (左右分区: 40% + 60%)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              大纲编辑                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 功能区 (40%)                    │ 编辑区 (60%)                              │
│ ┌─────────────────────────────┐ │ ┌─────────────────────────────────────────┐ │
│ │ 编辑模块选择                │ │ │ 内容编辑器                              │ │
│ │ ○ 小说标题                  │ │ │                                         │ │
│ │ ○ 核心主题                  │ │ │ 当前编辑: [选中的模块名称]              │ │
│ │ ○ 故事梗概                  │ │ │                                         │ │
│ │ ● 世界观设定                │ │ │ ┌─────────────────────────────────────┐ │ │
│ │                             │ │ │ │ [富文本编辑器区域]                  │ │ │
│ │ AI辅助功能                  │ │ │ │                                     │ │ │
│ │ AI模型: [下拉选择框]        │ │ │ │ [支持格式化文本编辑]                │ │ │
│ │ [AI重新生成]                │ │ │ │                                     │ │ │
│ │ [AI优化内容]                │ │ │ │ [自动保存功能]                      │ │ │
│ │ [AI扩展内容]                │ │ │ │                                     │ │ │
│ │ [内容润色]                  │ │ │ │ [实时字数统计]                      │ │ │
│ │                             │ │ │ │                                     │ │ │
│ │ 编辑工具                    │ │ │ │ [可调整高度的编辑区]                │ │ │
│ │ [撤销] [重做]               │ │ │ │                                     │ │ │
│ │ [查找] [替换]               │ │ │ │                                     │ │ │
│ │ [格式化] [清空]             │ │ │ └─────────────────────────────────────┘ │ │
│ │                             │ │ │                                         │ │
│ │ 版本历史                    │ │ │ 编辑工具栏                              │ │
│ │ ┌─────────────────────────┐ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ │ [版本列表显示区域]      │ │ │ │ │ [B] [I] [U] [字体] [大小] [颜色]    │ │ │
│ │ │                         │ │ │ │ │ [对齐] [列表] [缩进] [格式]         │ │ │
│ │ │ [可选择历史版本]        │ │ │ │ └─────────────────────────────────────┘ │ │
│ │ │                         │ │ │ │                                         │ │
│ │ │ [版本对比功能]          │ │ │ │ AI建议区                                │ │
│ │ └─────────────────────────┘ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │                             │ │ │ │ [AI生成的改进建议显示]              │ │ │
│ │ 快速操作                    │ │ │ │                                     │ │ │
│ │ [导入大纲]                  │ │ │ │ [可接受或拒绝建议]                  │ │ │
│ │ [导出大纲]                  │ │ │ │                                     │ │ │
│ │ [重置内容]                  │ │ │ │ [建议应用按钮]                      │ │ │
│ │                             │ │ │ └─────────────────────────────────────┘ │ │
│ │ 保存控制                    │ │ │                                         │ │
│ │ ┌─────────────────────────┐ │ │ │ 操作按钮                                │ │
│ │ │ [保存修改] [取消修改]   │ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ │ [另存版本] [预览]       │ │ │ │ │ [保存] [取消] [预览] [导出]         │ │ │
│ │ └─────────────────────────┘ │ │ │ └─────────────────────────────────────┘ │ │
│ └─────────────────────────────┘ │ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.8 章节编辑界面布局 (左右分区: 40% + 60%)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              章节编辑                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 功能区 (40%)                    │ 编辑区 (60%)                              │
│ ┌─────────────────────────────┐ │ ┌─────────────────────────────────────────┐ │
│ │ 章节列表                    │ │ │ 章节详情编辑                            │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │
│ │ │ [章节列表显示区域]      │ │ │ │ 基本信息                                │ │
│ │ │                         │ │ │ │ 章节号: [数字输入框]                    │ │
│ │ │ [支持拖拽排序]          │ │ │ │ 章节标题: [文本输入框]                  │ │
│ │ │                         │ │ │ │ 章节状态: [状态选择框]                  │ │
│ │ │ [显示章节状态]          │ │ │ │                                         │ │
│ │ │                         │ │ │ │ 章节摘要                                │ │
│ │ │ [显示字数统计]          │ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ │                         │ │ │ │ │ [多行文本输入框]                    │ │ │
│ │ │ [右键菜单操作]          │ │ │ │ │                                     │ │ │
│ │ └─────────────────────────┘ │ │ │ │ [支持富文本编辑]                    │ │ │
│ │                             │ │ │ │                                     │ │ │
│ │ 章节操作                    │ │ │ │ [实时字数统计]                      │ │ │
│ │ [添加章节]                  │ │ │ │                                     │ │ │
│ │ [删除章节]                  │ │ │ │ [自动保存提示]                      │ │ │
│ │ [复制章节]                  │ │ │ └─────────────────────────────────────┘ │ │
│ │ [移动章节]                  │ │ │                                         │ │
│ │ [批量操作]                  │ │ │ 关联设置                                │ │
│ │                             │ │ │ 关联角色: [多选复选框列表]              │ │
│ │ AI生成功能                  │ │ │ 关联场景: [多选复选框列表]              │ │
│ │ AI模型: [下拉选择框]        │ │ │ 关联道具: [多选复选框列表]              │ │
│ │ [基于大纲生成]              │ │ │                                         │ │
│ │ [AI生成标题]                │ │ │ AI辅助功能                              │ │
│ │ [AI生成摘要]                │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ [批量AI生成]                │ │ │ │ [AI生成标题] [AI生成摘要]           │ │ │
│ │                             │ │ │ │ [AI优化内容] [AI扩展内容]           │ │ │
│ │ 筛选排序                    │ │ │ │ [内容检查] [风格统一]               │ │ │
│ │ 状态筛选: [下拉选择]        │ │ │ └─────────────────────────────────────┘ │ │
│ │ 排序方式: [下拉选择]        │ │ │                                         │ │
│ │ [搜索框]                    │ │ │ 预览区域                                │ │
│ │                             │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ 批量操作                    │ │ │ │ [章节内容预览显示]                  │ │ │
│ │ [全选] [反选]               │ │ │ │                                     │ │ │
│ │ [批量删除]                  │ │ │ │ [只读模式显示]                      │ │ │
│ │ [批量导出]                  │ │ │ │                                     │ │ │
│ │                             │ │ │ │ [格式化显示]                        │ │ │
│ │ 保存控制                    │ │ │ └─────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │
│ │ │ [保存修改] [取消修改]   │ │ │ │ 操作按钮                                │ │
│ │ │ [导入章节] [导出章节]   │ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ └─────────────────────────┘ │ │ │ │ [保存] [取消] [预览] [生成内容]     │ │ │
│ │                             │ │ │ └─────────────────────────────────────┘ │ │
│ └─────────────────────────────┘ │ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.9 章节生成界面布局 (左右分区: 40% + 60%)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              章节生成                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 功能区 (40%)                    │ 生成区 (60%)                              │
│ ┌─────────────────────────────┐ │ ┌─────────────────────────────────────────┐ │
│ │ 章节选择                    │ │ │ 内容编辑器                              │ │
│ │ 选择章节: [下拉选择框]      │ │ │                                         │ │
│ │                             │ │ │ 章节标题: [显示当前章节标题]            │ │
│ │ 章节信息                    │ │ │                                         │ │
│ │ 章节号: [显示]              │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ 当前状态: [显示]            │ │ │ │ [富文本编辑器]                      │ │ │
│ │ 目标字数: [数字输入框]      │ │ │ │                                     │ │ │
│ │ 当前字数: [实时显示]        │ │ │ │ [支持格式化编辑]                    │ │ │
│ │                             │ │ │ │                                     │ │ │
│ │ 生成设置                    │ │ │ │ [语法高亮显示]                      │ │ │
│ │ AI模型: [下拉选择框]        │ │ │ │                                     │ │ │
│ │ 写作风格: [下拉选择框]      │ │ │ │ [实时字数统计]                      │ │ │
│ │ 生成模式: [下拉选择框]      │ │ │ │                                     │ │ │
│ │ 创意程度: [滑动条]          │ │ │ │ [自动保存功能]                      │ │ │
│ │                             │ │ │ │                                     │ │ │
│ │ 关联要素                    │ │ │ │ [支持撤销重做]                      │ │ │
│ │ 关联角色: [多选列表]        │ │ │ │                                     │ │ │
│ │ 关联场景: [多选列表]        │ │ │ │ [支持查找替换]                      │ │ │
│ │ 关联道具: [多选列表]        │ │ │ │                                     │ │ │
│ │ 关联情节: [多选列表]        │ │ │ │ [可调整编辑器高度]                  │ │ │
│ │                             │ │ │ └─────────────────────────────────────┘ │ │
│ │ 上下文设置                  │ │ │                                         │ │
│ │ 参考章节: [多选列表]        │ │ │ 编辑工具栏                              │ │
│ │ 上下文长度: [滑动条]        │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ ☑ 保持角色一致性            │ │ │ │ [粗体] [斜体] [下划线] [字体] [大小] │ │ │
│ │ ☑ 考虑前文情节              │ │ │ │ [颜色] [对齐] [列表] [缩进] [格式]  │ │ │
│ │ ☑ 维持写作风格              │ │ │ └─────────────────────────────────────┘ │ │
│ │                             │ │ │                                         │ │
│ │ 生成选项                    │ │ │ AI辅助工具                              │ │
│ │ ☑ 降AI味处理                │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ ☑ 自动分段                  │ │ │ │ [续写] [扩写] [改写] [润色]         │ │ │
│ │ ☑ 添加对话                  │ │ │ │ [风格调整] [语法检查] [降AI味]      │ │ │
│ │ ☑ 环境描写                  │ │ │ │ [选中文本处理] [智能建议]           │ │ │
│ │ ☑ 心理描写                  │ │ │ └─────────────────────────────────────┘ │ │
│ │                             │ │ │                                         │ │
│ │ 生成控制                    │ │ │ 生成状态                                │ │
│ │ ┌─────────────────────────┐ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ │ [生成内容] [继续生成]   │ │ │ │ │ 状态: [等待/生成中/完成/错误]       │ │ │
│ │ │ [暂停生成] [停止生成]   │ │ │ │ │ 进度: [进度条]                      │ │ │
│ │ └─────────────────────────┘ │ │ │ │ 已生成: [字数] / 目标: [字数]       │ │ │
│ │                             │ │ │ │ 用时: [生成耗时] 速度: [字/秒]      │ │ │
│ │ 版本管理                    │ │ │ └─────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │
│ │ │ [版本历史列表]          │ │ │ │ 操作按钮                                │ │
│ │ │                         │ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ │ [版本对比功能]          │ │ │ │ │ [保存章节] [另存版本] [导出]        │ │ │
│ │ │                         │ │ │ │ │ [预览] [打印] [复制]                │ │ │
│ │ │ [版本恢复功能]          │ │ │ │ └─────────────────────────────────────┘ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │
│ └─────────────────────────────┘ │ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.10 章节分析界面布局 (左右分区: 40% + 60%)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              章节分析                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 功能区 (40%)                    │ 分析结果区 (60%)                          │
│ ┌─────────────────────────────┐ │ ┌─────────────────────────────────────────┐ │
│ │ 章节选择                    │ │ │ 分析报告显示                            │ │
│ │ 选择章节: [下拉选择框]      │ │ │                                         │ │
│ │                             │ │ │ 分析类型: [当前选中的分析类型]          │ │
│ │ 章节信息                    │ │ │                                         │ │
│ │ 章节号: [显示]              │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ 章节标题: [显示]            │ │ │ │ [分析结果显示区域]                  │ │ │
│ │ 字数统计: [显示]            │ │ │ │                                     │ │ │
│ │ 创建时间: [显示]            │ │ │ │ [支持富文本显示]                    │ │ │
│ │ 修改时间: [显示]            │ │ │ │                                     │ │ │
│ │                             │ │ │ │ [可滚动内容区域]                    │ │ │
│ │ 分析选项                    │ │ │ │                                     │ │ │
│ │ ☑ 核心剧情分析              │ │ │ │ [支持复制内容]                      │ │ │
│ │ ☑ 故事梗概提取              │ │ │ │                                     │ │ │
│ │ ☑ 优缺点分析                │ │ │ │ [支持导出功能]                      │ │ │
│ │ ☑ 角色行为分析              │ │ │ │                                     │ │ │
│ │ ☑ 情节连贯性                │ │ │ │ [支持打印功能]                      │ │ │
│ │ ☑ 语言风格分析              │ │ │ │                                     │ │ │
│ │ ☑ 改进建议                  │ │ │ │ [分析结果分类显示]                  │ │ │
│ │ ☑ 质量评分                  │ │ │ │                                     │ │ │
│ │                             │ │ │ │ [图表数据可视化]                    │ │ │
│ │ AI模型设置                  │ │ │ └─────────────────────────────────────┘ │ │
│ │ 分析模型: [下拉选择框]      │ │ │                                         │ │
│ │ 分析深度: [滑动条]          │ │ │ 分析工具栏                              │ │
│ │ 对比模式: [复选框]          │ │ │ ┌─────────────────────────────────────┐ │ │
│ │                             │ │ │ │ [切换视图] [筛选结果] [搜索内容]    │ │ │
│ │ 对比设置                    │ │ │ │ [高亮关键词] [折叠展开] [字体调整]  │ │ │
│ │ 对比章节: [多选列表]        │ │ │ └─────────────────────────────────────┘ │ │
│ │ 对比维度: [多选列表]        │ │ │                                         │ │
│ │                             │ │ │ 改进建议区                              │ │
│ │ 分析控制                    │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ [AI生成的改进建议]                  │ │ │
│ │ │ [开始分析] [重新分析]   │ │ │ │ │                                     │ │ │
│ │ │ [停止分析] [清空结果]   │ │ │ │ │ [可操作的建议项目]                  │ │ │
│ │ └─────────────────────────┘ │ │ │ │                                     │ │ │
│ │                             │ │ │ │ [一键应用建议]                      │ │ │
│ │ 分析历史                    │ │ │ │                                     │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ [建议评分系统]                      │ │ │
│ │ │ [历史分析记录]          │ │ │ │ └─────────────────────────────────────┘ │ │
│ │ │                         │ │ │ │                                         │ │
│ │ │ [按时间排序]            │ │ │ │ 分析状态                                │ │
│ │ │                         │ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ │ [快速加载历史]          │ │ │ │ │ 状态: [分析中/完成/错误]            │ │ │
│ │ │                         │ │ │ │ │ 进度: [分析进度条]                  │ │ │
│ │ │ [对比历史结果]          │ │ │ │ │ 用时: [分析耗时]                    │ │ │
│ │ └─────────────────────────┘ │ │ │ │ 质量评分: [综合评分显示]            │ │ │
│ │                             │ │ │ └─────────────────────────────────────┘ │ │
│ │ 导出选项                    │ │ │                                         │ │
│ │ ☑ 导出分析报告              │ │ │ 操作按钮                                │ │
│ │ ☑ 导出改进建议              │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ ☑ 导出对比结果              │ │ │ │ [应用改进] [保存报告] [导出数据]    │ │ │
│ │ 格式: [下拉选择框]          │ │ │ │ [复制结果] [打印报告] [重新分析]    │ │ │
│ │                             │ │ │ └─────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │
│ │ │ [章节改进] [导出报告]   │ │ │ │                                         │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │
│ └─────────────────────────────┘ │ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.11 人物编辑界面布局 (左右分区: 40% + 60%)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              人物编辑                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 功能区 (40%)                    │ 编辑区 (60%)                              │
│ ┌─────────────────────────────┐ │ ┌─────────────────────────────────────────┐ │
│ │ 角色分类筛选                │ │ │ 角色详情编辑                            │ │
│ │ ● 全部角色                  │ │ │                                         │ │
│ │ ○ 主角                      │ │ │ 基本信息                                │ │
│ │ ○ 重要角色                  │ │ │ 角色姓名: [文本输入框]                  │ │
│ │ ○ 配角                      │ │ │ 角色类型: [下拉选择框]                  │ │
│ │ ○ 反派                      │ │ │ 性别: [单选按钮组]                      │ │
│ │ ○ 龙套                      │ │ │ 年龄: [数字输入框]                      │ │
│ │                             │ │ │ 年龄: [数字输入框]                      │ │
│ │ 搜索筛选                    │ │ │ 身份职业: [文本输入框]                  │ │
│ │ [搜索角色名称输入框]        │ │ │                                         │ │
│ │ [高级筛选按钮]              │ │ │ 外貌描述                                │ │
│ │                             │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ 角色列表                    │ │ │ │ [多行文本输入框]                    │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                     │ │ │
│ │ │ [角色列表显示区域]      │ │ │ │ │ [支持富文本编辑]                    │ │ │
│ │ │                         │ │ │ │ │                                     │ │ │
│ │ │ [显示角色头像]          │ │ │ │ │ [实时字数统计]                      │ │ │
│ │ │                         │ │ │ │ │                                     │ │ │
│ │ │ [显示角色基本信息]      │ │ │ │ │ [自动保存功能]                      │ │ │
│ │ │                         │ │ │ │ └─────────────────────────────────────┘ │ │
│ │ │ [支持多选操作]          │ │ │ │                                         │ │
│ │ │                         │ │ │ │ 性格特点                                │ │
│ │ │ [右键菜单功能]          │ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ │                         │ │ │ │ │ [多行文本输入框]                    │ │ │
│ │ │ [拖拽排序支持]          │ │ │ │ │                                     │ │ │
│ │ └─────────────────────────┘ │ │ │ │ [标签化输入支持]                    │ │ │
│ │                             │ │ │ │                                     │ │ │
│ │ 角色操作                    │ │ │ │ [预设性格模板]                      │ │ │
│ │ [添加角色]                  │ │ │ │                                     │ │ │
│ │ [删除角色]                  │ │ │ │ [性格冲突检测]                      │ │ │
│ │ [复制角色]                  │ │ │ └─────────────────────────────────────┘ │ │
│ │ [导入角色]                  │ │ │                                         │ │
│ │ [导出角色]                  │ │ │ 背景故事                                │ │
│ │ [批量操作]                  │ │ │ ┌─────────────────────────────────────┐ │ │
│ │                             │ │ │ │ [富文本编辑器]                      │ │ │
│ │ AI生成功能                  │ │ │ │                                     │ │ │
│ │ AI模型: [下拉选择框]        │ │ │ │ [支持章节引用]                      │ │ │
│ │ [AI生成角色]                │ │ │ │                                     │ │ │
│ │ [AI完善设定]                │ │ │ │ [时间线管理]                        │ │ │
│ │ [AI生成头像]                │ │ │ │                                     │ │ │
│ │ [AI关系分析]                │ │ │ │ [关键事件标记]                      │ │ │
│ │ [批量AI优化]                │ │ │ │                                     │ │ │
│ │                             │ │ │ │ [版本历史记录]                      │ │ │
│ │ 模板管理                    │ │ │ └─────────────────────────────────────┘ │ │
│ │ [角色模板库]                │ │ │                                         │ │
│ │ [保存为模板]                │ │ │ AI辅助功能                              │ │
│ │ [应用模板]                  │ │ │ ┌─────────────────────────────────────┐ │ │
│ │                             │ │ │ │ [AI生成外貌] [AI生成性格]           │ │ │
│ │ 数据统计                    │ │ │ │ [AI生成背景] [AI完善设定]           │ │ │
│ │ 总角色数: [显示]            │ │ │ │ [AI关系建议] [AI冲突检测]           │ │ │
│ │ 各类型统计: [显示]          │ │ │ └─────────────────────────────────────┘ │ │
│ │ 完善度统计: [显示]          │ │ │                                         │ │
│ │                             │ │ │ 关联信息                                │ │
│ │ 保存控制                    │ │ │ 出场章节: [多选列表]                    │ │
│ │ ┌─────────────────────────┐ │ │ │ 关联角色: [多选列表]                    │ │
│ │ │ [保存修改] [取消修改]   │ │ │ │ 关联道具: [多选列表]                    │ │
│ │ │ [导入数据] [导出数据]   │ │ │ │ 关联场景: [多选列表]                    │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │
│ │                             │ │ │ 操作按钮                                │ │
│ │                             │ │ │ ┌─────────────────────────────────────┐ │ │
│ │                             │ │ │ │ [保存] [取消] [预览] [复制]         │ │ │
│ │                             │ │ │ │ [导出] [删除] [生成卡片]            │ │ │
│ │                             │ │ │ └─────────────────────────────────────┘ │ │
│ └─────────────────────────────┘ │ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.12 人物关系图界面布局 (左右分区: 40% + 60%)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                            人物关系图                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 功能区 (40%)                    │ 关系图区 (60%)                            │
│ ┌─────────────────────────────┐ │ ┌─────────────────────────────────────────┐ │
│ │ 关系管理                    │ │ │ 可视化关系图                            │ │
│ │                             │ │ │                                         │ │
│ │ 添加关系                    │ │ │ [SVG/Canvas关系图显示区域]              │ │
│ │ 角色1: [下拉选择框]         │ │ │                                         │ │
│ │ 角色2: [下拉选择框]         │ │ │ [支持拖拽移动节点]                      │ │
│ │ 关系类型: [下拉选择框]      │ │ │                                         │ │
│ │ 关系强度: [滑动条]          │ │ │ [支持缩放操作]                          │ │
│ │                             │ │ │                                         │ │
│ │ 关系描述:                   │ │ │ [支持节点选择]                          │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │
│ │ │ [多行文本输入框]        │ │ │ │ [支持连线编辑]                          │ │
│ │ │                         │ │ │ │                                         │ │
│ │ │ [支持富文本编辑]        │ │ │ │ [实时布局算法]                          │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │
│ │                             │ │ │ [节点信息提示]                          │ │
│ │ [添加关系] [更新关系]       │ │ │                                         │ │
│ │                             │ │ │ [关系线条样式]                          │ │
│ │ 关系类型管理                │ │ │                                         │ │
│ │ ┌─────────────────────────┐ │ │ │ [图例显示区域]                          │ │
│ │ │ [关系类型列表]          │ │ │ │                                         │ │
│ │ │                         │ │ │ │ [导航控制器]                            │ │
│ │ │ [自定义关系类型]        │ │ │ │                                         │ │
│ │ │                         │ │ │ │ [全屏显示支持]                          │ │
│ │ │ [关系颜色设置]          │ │ │ └─────────────────────────────────────────┘ │ │
│ │ │                         │ │ │                                         │ │
│ │ │ [关系图标设置]          │ │ │ 图形控制工具栏                          │ │
│ │ └─────────────────────────┘ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │                             │ │ │ │ [选择] [移动] [缩放] [适应] [居中]  │ │ │
│ │ 现有关系列表                │ │ │ │ [布局] [筛选] [搜索] [导出] [打印]  │ │ │
│ │ ┌─────────────────────────┐ │ │ │ └─────────────────────────────────────┘ │ │
│ │ │ [关系列表显示区域]      │ │ │ │                                         │ │
│ │ │                         │ │ │ │ 布局选项                                │ │
│ │ │ [支持筛选排序]          │ │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ │                         │ │ │ │ │ ○ 力导向布局                        │ │ │
│ │ │ [支持批量操作]          │ │ │ │ │ ○ 层次布局                          │ │ │
│ │ │                         │ │ │ │ │ ○ 圆形布局                          │ │ │
│ │ │ [关系编辑功能]          │ │ │ │ │ ○ 网格布局                          │ │ │
│ │ │                         │ │ │ │ │ ○ 自定义布局                        │ │ │
│ │ │ [关系删除功能]          │ │ │ │ └─────────────────────────────────────┘ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │
│ │                             │ │ │ 显示选项                                │ │
│ │ 筛选显示                    │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ 关系类型筛选:               │ │ │ │ ☑ 显示角色名称                      │ │ │
│ │ ☑ 亲情 ☑ 友情 ☑ 爱情       │ │ │ │ ☑ 显示关系标签                      │ │ │
│ │ ☑ 敌对 ☑ 合作 ☑ 师徒       │ │ │ │ ☑ 显示关系强度                      │ │ │
│ │ ☑ 竞争 ☑ 其他               │ │ │ │ ☑ 显示角色头像                      │ │ │
│ │                             │ │ │ │ ☑ 显示关系方向                      │ │ │
│ │ 角色类型筛选:               │ │ │ │ ☑ 显示关系描述                      │ │ │
│ │ ☑ 主角 ☑ 重要角色           │ │ │ └─────────────────────────────────────┘ │ │
│ │ ☑ 配角 ☑ 反派 ☑ 龙套        │ │ │                                         │ │
│ │                             │ │ │ 操作提示                                │ │
│ │ 图形设置                    │ │ │ ┌─────────────────────────────────────┐ │ │
│ │ 节点大小: [滑动条]          │ │ │ │ • 拖拽节点调整位置                  │ │ │
│ │ 连线粗细: [滑动条]          │ │ │ │ • 点击节点查看详情                  │ │ │
│ │ 字体大小: [滑动条]          │ │ │ │ • 双击连线编辑关系                  │ │ │
│ │ 透明度: [滑动条]            │ │ │ │ • 滚轮缩放图形                      │ │ │
│ │                             │ │ │ │ • 右键显示菜单                      │ │ │
│ │ 导出选项                    │ │ │ └─────────────────────────────────────┘ │ │
│ │ [导出数据]                  │ │ │                                         │ │
│ │ [导出报告]                  │ │ │ 操作按钮                                │ │
│ │ [保存配置]                  │ │ │ ┌─────────────────────────────────────┐ │ │
│ │                             │ │ │ │ [保存关系图] [重置布局] [全屏]      │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ [导出数据] [保存配置] [打印]        │ │ │
│ │ │ [重新布局] [保存图形]   │ │ │ │ └─────────────────────────────────────┘ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │
│ └─────────────────────────────┘ │ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.13 统计信息界面布局 (全屏显示)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              统计信息                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 项目概览统计                                                               │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 项目名称: [当前项目名称]                                                │ │
│ │ 创建时间: [项目创建日期]     最后更新: [最后修改时间]                   │ │
│ │ 总章节数: [章节总数]         已完成: [完成章节数]                       │ │
│ │ 总字数: [总字数统计]         平均每章: [平均字数]                       │ │
│ │ 完成进度: [进度百分比]       预计完成: [预计完成时间]                   │ │
│ │                                                                         │ │
│ │ 进度条显示: [████████████████████████████████████████████████████████] │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 章节统计详情                                                               │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ [表格显示区域]                                                          │ │
│ │                                                                         │ │
│ │ 章节号 │ 章节标题         │ 字数   │ 状态   │ 创建时间 │ 修改时间 │ 操作 │ │
│ │ ────── │ ──────────────── │ ────── │ ────── │ ──────── │ ──────── │ ──── │ │
│ │ [数据行显示区域]                                                        │ │
│ │                                                                         │ │
│ │ [支持排序功能]                                                          │ │
│ │ [支持筛选功能]                                                          │ │
│ │ [支持分页显示]                                                          │ │
│ │ [支持导出功能]                                                          │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 创作趋势分析                                                               │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 时间范围: [日期选择器] 到 [日期选择器]  统计维度: [下拉选择框]          │ │
│ │                                                                         │ │
│ │ [图表显示区域]                                                          │ │
│ │                                                                         │ │
│ │ [支持多种图表类型: 折线图/柱状图/饼图]                                  │ │
│ │ [支持数据钻取功能]                                                      │ │
│ │ [支持图表导出功能]                                                      │ │
│ │ [支持数据对比功能]                                                      │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 写作效率统计                                                               │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 平均日产字数: [数值显示]    最高日产: [数值显示]                        │ │
│ │ 连续创作天数: [数值显示]    总创作天数: [数值显示]                      │ │
│ │ 平均创作时长: [时长显示]    总创作时长: [时长显示]                      │ │
│ │ 创作效率趋势: [趋势图表显示]                                            │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 角色统计分析                                                               │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 总角色数: [数值]  主角: [数值]  重要角色: [数值]  配角: [数值]          │ │
│ │ 角色出场频率统计: [图表显示]                                            │ │
│ │ 角色关系复杂度: [数值显示]                                              │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ AI使用统计                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ AI调用次数: [数值]       成功率: [百分比]                               │ │
│ │ 生成字数: [数值]         平均响应时间: [时间]                           │ │
│ │ 最常用模型: [模型名称]   使用分布: [饼图显示]                           │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 操作控制区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ [刷新统计] [导出报告] [设置目标] [数据对比] [历史统计] [清除缓存]       │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.14 AI聊天界面布局 (全屏显示)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              AI聊天                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 模型选择区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 当前模型: [下拉选择框]  状态: [连接状态显示]  [测试连接] [切换模型]     │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 对话显示区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ [聊天消息显示区域]                                                      │ │
│ │                                                                         │ │
│ │ [支持消息气泡显示]                                                      │ │
│ │ [支持富文本显示]                                                        │ │
│ │ [支持代码高亮]                                                          │ │
│ │ [支持文本格式化]                                                        │ │
│ │ [支持消息复制]                                                          │ │
│ │ [支持消息导出]                                                          │ │
│ │ [支持消息搜索]                                                          │ │
│ │ [支持自动滚动]                                                          │ │
│ │ [支持消息时间戳]                                                        │ │
│ │ [支持消息状态显示]                                                      │ │
│ │                                                                         │ │
│ │ [可调整显示区域高度]                                                    │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 输入控制区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ [多行文本输入框]                                                        │ │
│ │                                                                         │ │
│ │ [支持富文本输入]                                                        │ │
│ │ [支持快捷键操作]                                                        │ │
│ │ [支持历史记录]                                                          │ │
│ │ [支持自动补全]                                                          │ │
│ │ [实时字数统计]                                                          │ │
│ │                                                                         │ │
│ │ 输入工具栏: [格式化] [插入代码] [插入表格] [文本工具] [清空] [历史]     │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 功能控制区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 对话设置:                                                               │ │
│ │ 温度: [滑动条]  最大长度: [数字输入]  上下文: [数字输入]                │ │
│ │                                                                         │ │
│ │ 快捷功能:                                                               │ │
│ │ [写作建议] [语法检查] [风格分析] [内容扩展] [翻译] [总结]               │ │
│ │                                                                         │ │
│ │ 对话管理:                                                               │ │
│ │ [新建对话] [保存对话] [加载对话] [导出对话] [清空对话] [删除对话]       │ │
│ │                                                                         │ │
│ │ 发送控制:                                                               │ │
│ │ [发送消息] [停止生成] [重新生成] [快捷输入] [快捷发送]                  │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 侧边功能区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 对话历史: [对话列表显示]                                                │ │
│ │ 常用提示: [提示词快捷按钮]                                              │ │
│ │ 模型状态: [各模型连接状态]                                              │ │
│ │ 使用统计: [调用次数/成功率等]                                           │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.15 提示词库界面布局 (全屏显示)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              提示词库                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 分类筛选区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 模板分类: [全部] [大纲] [章节] [人物] [世界观] [润色] [其他] [自定义]   │ │
│ │ 搜索筛选: [搜索输入框] [高级筛选] [标签筛选] [收藏筛选]                 │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 主内容区 (左右分区: 30% + 70%)                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 模板列表区 (30%)              │ 模板详情区 (70%)                        │ │
│ │ ┌─────────────────────────────┐ │ ┌─────────────────────────────────────┐ │ │
│ │ │ [提示词模板列表]            │ │ │ 模板详情显示                        │ │ │
│ │ │                             │ │ │                                     │ │ │
│ │ │ [显示模板名称]              │ │ │ 模板名称: [显示/编辑]               │ │ │
│ │ │ [显示模板分类]              │ │ │ 模板分类: [下拉选择]                │ │ │
│ │ │ [显示模板描述]              │ │ │ 模板描述: [文本输入框]              │ │ │
│ │ │ [显示使用次数]              │ │ │ 创建时间: [显示]                    │ │ │
│ │ │ [显示收藏状态]              │ │ │ 使用次数: [显示]                    │ │ │
│ │ │ [显示模板标签]              │ │ │                                     │ │ │
│ │ │                             │ │ │ 模板内容:                           │ │ │
│ │ │ [支持拖拽排序]              │ │ │ ┌─────────────────────────────────┐ │ │ │
│ │ │ [支持多选操作]              │ │ │ │ [富文本编辑器]                  │ │ │ │
│ │ │ [支持右键菜单]              │ │ │ │                                 │ │ │ │
│ │ │                             │ │ │ │ [支持语法高亮]                  │ │ │ │
│ │ │ [分页显示控制]              │ │ │ │ [支持变量插入]                  │ │ │ │
│ │ └─────────────────────────────┘ │ │ │ [支持模板预览]                  │ │ │ │
│ │                                 │ │ │ [支持格式化]                    │ │ │ │
│ │ 操作控制区                      │ │ │ [实时字数统计]                  │ │ │ │
│ │ ┌─────────────────────────────┐ │ │ │                                 │ │ │ │
│ │ │ [新建模板]                  │ │ │ │ [自动保存功能]                  │ │ │ │
│ │ │ [导入模板]                  │ │ │ └─────────────────────────────────┘ │ │ │
│ │ │ [导出模板]                  │ │ │                                     │ │ │
│ │ │ [批量操作]                  │ │ │ 变量设置:                           │ │ │
│ │ │ [删除模板]                  │ │ │ ┌─────────────────────────────────┐ │ │ │
│ │ │ [复制模板]                  │ │ │ │ [变量列表显示]                  │ │ │ │
│ │ │                             │ │ │ │ [变量添加删除]                  │ │ │ │
│ │ │ [收藏管理]                  │ │ │ │ [变量类型设置]                  │ │ │ │
│ │ │ [标签管理]                  │ │ │ │ [默认值设置]                    │ │ │ │
│ │ │ [分类管理]                  │ │ │ └─────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────┘ │ │                                     │ │ │
│ │                                 │ │ 使用示例:                           │ │ │
│ │                                 │ │ ┌─────────────────────────────────┐ │ │ │
│ │                                 │ │ │ [示例输入显示]                  │ │ │ │
│ │                                 │ │ │ [示例输出显示]                  │ │ │ │
│ │                                 │ │ │ [效果预览功能]                  │ │ │ │
│ │                                 │ │ └─────────────────────────────────┘ │ │ │
│ │                                 │ │                                     │ │ │
│ │                                 │ │ 操作按钮:                           │ │ │
│ │                                 │ │ ┌─────────────────────────────────┐ │ │ │
│ │                                 │ │ │ [保存] [取消] [预览] [测试]     │ │ │ │
│ │                                 │ │ │ [复制] [导出] [收藏] [备份]     │ │ │ │
│ │                                 │ │ └─────────────────────────────────┘ │ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.16 上下文管理界面布局 (全屏显示)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                            上下文管理                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 项目上下文概览                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 当前项目: [项目名称]  总章节: [数量]  已分析: [数量]  待处理: [数量]    │ │
│ │ 上下文状态: [正常/更新中/错误]  最后更新: [时间]  [立即更新] [重建索引] │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 主内容区 (左右分区: 40% + 60%)                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 管理控制区 (40%)              │ 内容显示区 (60%)                        │ │
│ │ ┌─────────────────────────────┐ │ ┌─────────────────────────────────────┐ │ │
│ │ │ 章节选择                    │ │ │ 上下文内容显示                      │ │ │
│ │ │ [章节列表显示]              │ │ │                                     │ │ │
│ │ │                             │ │ │ 选中章节: [章节标题显示]            │ │ │
│ │ │ [支持多选操作]              │ │ │                                     │ │ │
│ │ │ [支持筛选搜索]              │ │ │ ┌─────────────────────────────────┐ │ │ │
│ │ │ [显示分析状态]              │ │ │ │ [章节内容显示区域]              │ │ │ │
│ │ │                             │ │ │ │                                 │ │ │ │
│ │ │ 上下文设置                  │ │ │ │ [支持语法高亮]                  │ │ │ │
│ │ │ 分析深度: [滑动条]          │ │ │ │ [支持关键词标记]                │ │ │ │
│ │ │ 关联范围: [数字输入]        │ │ │ │ [支持实体识别]                  │ │ │ │
│ │ │ 更新频率: [下拉选择]        │ │ │ │ [支持情节标记]                  │ │ │ │
│ │ │                             │ │ │ │ [支持角色标记]                  │ │ │ │
│ │ │ 分析选项                    │ │ │ │                                 │ │ │ │
│ │ │ ☑ 角色关系分析              │ │ │ │ [可调整显示区域]                │ │ │ │
│ │ │ ☑ 情节线索提取              │ │ │ └─────────────────────────────────┘ │ │ │
│ │ │ ☑ 时间线分析                │ │ │                                     │ │ │
│ │ │ ☑ 地点场景识别              │ │ │ 分析结果显示                        │ │ │
│ │ │ ☑ 伏笔线索跟踪              │ │ │ ┌─────────────────────────────────┐ │ │ │
│ │ │ ☑ 矛盾冲突检测              │ │ │ │ [分析结果标签页]                │ │ │ │
│ │ │                             │ │ │ │                                 │ │ │ │
│ │ │ 处理控制                    │ │ │ │ [角色] [情节] [时间] [地点]     │ │ │ │
│ │ │ [分析选中章节]              │ │ │ │ [伏笔] [冲突] [关系] [其他]     │ │ │ │
│ │ │ [批量分析]                  │ │ │ │                                 │ │ │ │
│ │ │ [重新分析]                  │ │ │ │ [结构化数据显示]                │ │ │ │
│ │ │ [清除分析]                  │ │ │ │ [支持筛选排序]                  │ │ │ │
│ │ │                             │ │ │ │ [支持导出功能]                  │ │ │ │
│ │ │ 导出选项                    │ │ │ └─────────────────────────────────┘ │ │ │
│ │ │ ☑ 导出分析报告              │ │ │                                     │ │ │
│ │ │ ☑ 导出关系图谱              │ │ │ 智能建议区                          │ │ │
│ │ │ ☑ 导出时间线                │ │ │ ┌─────────────────────────────────┐ │ │ │
│ │ │ 格式: [下拉选择]            │ │ │ │ [AI生成的上下文建议]            │ │ │ │
│ │ │                             │ │ │ │                                 │ │ │ │
│ │ │ 系统状态                    │ │ │ │ [连贯性检查结果]                │ │ │ │
│ │ │ 处理队列: [数量显示]        │ │ │ │ [矛盾冲突提醒]                  │ │ │ │
│ │ │ 内存使用: [使用量显示]      │ │ │ │ [改进建议列表]                  │ │ │ │
│ │ │ 索引状态: [状态显示]        │ │ │ │                                 │ │ │ │
│ │ │                             │ │ │ │ [一键应用建议]                  │ │ │ │
│ │ │ ┌─────────────────────────┐ │ │ │ └─────────────────────────────────┘ │ │ │
│ │ │ │ [保存设置] [重置设置]   │ │ │ │                                     │ │ │
│ │ │ └─────────────────────────┘ │ │ │ 操作按钮                            │ │ │
│ │ └─────────────────────────────┘ │ │ ┌─────────────────────────────────┐ │ │ │
│ │                                 │ │ │ [刷新分析] [导出数据] [清除]    │ │ │ │
│ │                                 │ │ │ [设置] [帮助] [反馈]            │ │ │ │
│ │                                 │ │ └─────────────────────────────────┘ │ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.17 向量库检索界面布局 (全屏显示)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                            向量库检索                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 检索配置区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 嵌入模型: [下拉选择框]  状态: [连接状态]  [测试连接] [重建索引]          │ │
│ │ 检索范围: [全部内容] [当前项目] [指定章节] [指定类型]                   │ │
│ │ 相似度阈值: [滑动条]  返回数量: [数字输入]  检索模式: [下拉选择]        │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 主内容区 (左右分区: 40% + 60%)                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 检索控制区 (40%)              │ 结果显示区 (60%)                        │ │
│ │ ┌─────────────────────────────┐ │ ┌─────────────────────────────────────┐ │ │
│ │ │ 查询输入                    │ │ │ 检索结果显示                        │ │ │
│ │ │ ┌─────────────────────────┐ │ │ │                                     │ │ │
│ │ │ │ [多行文本输入框]        │ │ │ │ 查询: [显示当前查询内容]            │ │ │
│ │ │ │                         │ │ │ │ 结果数量: [显示结果数量]            │ │ │
│ │ │ │ [支持自然语言查询]      │ │ │ │ 检索用时: [显示检索时间]            │ │ │
│ │ │ │ [支持关键词查询]        │ │ │ │                                     │ │ │
│ │ │ │ [支持语义查询]          │ │ │ │ ┌─────────────────────────────────┐ │ │ │
│ │ │ │                         │ │ │ │ │ [检索结果列表显示]              │ │ │ │
│ │ │ │ [实时查询建议]          │ │ │ │ │                                 │ │ │ │
│ │ │ └─────────────────────────┘ │ │ │ │ [按相似度排序显示]              │ │ │ │
│ │ │                             │ │ │ │ [显示相似度分数]                │ │ │ │
│ │ │ 查询历史                    │ │ │ │ [显示来源信息]                  │ │ │ │
│ │ │ ┌─────────────────────────┐ │ │ │ │ [显示内容摘要]                  │ │ │ │
│ │ │ │ [历史查询列表]          │ │ │ │ [支持内容预览]                  │ │ │ │
│ │ │ │                         │ │ │ │                                 │ │ │ │
│ │ │ │ [快速重用查询]          │ │ │ │ [支持结果筛选]                  │ │ │ │
│ │ │ │ [查询收藏功能]          │ │ │ │ [支持结果排序]                  │ │ │ │
│ │ │ │ [查询统计信息]          │ │ │ │ [支持批量操作]                  │ │ │ │
│ │ │ └─────────────────────────┘ │ │ │ │                                 │ │ │ │
│ │ │                             │ │ │ │ [分页显示控制]                  │ │ │ │
│ │ │ 高级选项                    │ │ │ └─────────────────────────────────┘ │ │ │
│ │ │ 内容类型:                   │ │ │                                     │ │ │
│ │ │ ☑ 章节内容                  │ │ │ 详情显示区                          │ │ │
│ │ │ ☑ 角色信息                  │ │ │ ┌─────────────────────────────────┐ │ │ │
│ │ │ ☑ 大纲内容                  │ │ │ │ [选中结果的详细内容显示]        │ │ │ │
│ │ │ ☑ 世界观设定                │ │ │ │                                 │ │ │ │
│ │ │                             │ │ │ │ [支持富文本显示]                │ │ │ │
│ │ │ 时间范围:                   │ │ │ │ [支持关键词高亮]                │ │ │ │
│ │ │ 开始: [日期选择]            │ │ │ │ [支持内容复制]                  │ │ │ │
│ │ │ 结束: [日期选择]            │ │ │ │ [支持内容编辑]                  │ │ │ │
│ │ │                             │ │ │ │                                 │ │ │ │
│ │ │ 检索控制                    │ │ │ │ [显示元数据信息]                │ │ │ │
│ │ │ ┌─────────────────────────┐ │ │ │ │ [显示关联信息]                  │ │ │ │
│ │ │ │ [开始检索] [停止检索]   │ │ │ │ [显示引用信息]                  │ │ │ │
│ │ │ │ [清空结果] [导出结果]   │ │ │ └─────────────────────────────────┘ │ │ │
│ │ │ └─────────────────────────┘ │ │ │                                     │ │ │
│ │ │                             │ │ │ 操作工具栏                          │ │ │
│ │ │ 索引管理                    │ │ │ ┌─────────────────────────────────┐ │ │ │
│ │ │ 索引状态: [状态显示]        │ │ │ │ [复制结果] [导出结果] [保存]    │ │ │ │
│ │ │ 索引大小: [大小显示]        │ │ │ │ [添加收藏] [创建笔记] [应用]    │ │ │ │
│ │ │ 最后更新: [时间显示]        │ │ │ └─────────────────────────────────┘ │ │ │
│ │ │                             │ │ │                                     │ │ │
│ │ │ [重建索引]                  │ │ │ 统计信息                            │ │ │
│ │ │ [优化索引]                  │ │ │ ┌─────────────────────────────────┐ │ │ │
│ │ │ [清理索引]                  │ │ │ │ 索引文档数: [数量]              │ │ │ │
│ │ │                             │ │ │ │ 查询次数: [次数]                │ │ │ │
│ │ │ ┌─────────────────────────┐ │ │ │ │ 平均响应时间: [时间]            │ │ │ │
│ │ │ │ [保存设置] [重置设置]   │ │ │ │ │ 命中率: [百分比]                │ │ │ │
│ │ │ └─────────────────────────┘ │ │ │ └─────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────┘ │ └─────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.18 设置界面布局 (全屏显示)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              系统设置                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 设置分类导航                                                               │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ [AI配置] [界面设置] [功能设置] [数据管理] [高级设置] [关于]             │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ AI配置管理区                                                               │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 服务商配置                                                              │ │
│ │ ┌─────────────────────────────────────────────────────────────────────┐ │ │
│ │ │ OpenAI配置                                                          │ │ │
│ │ │ API密钥: [密码输入框] [显示/隐藏] [测试连接]                        │ │ │
│ │ │ 模型名称: [下拉选择框] [自定义输入]                                 │ │ │
│ │ │ API地址: [文本输入框] [重置默认]                                    │ │ │
│ │ │ 连接状态: [状态显示] 最后测试: [时间显示]                           │ │ │
│ │ │ [保存配置] [测试连接] [重置配置]                                    │ │ │
│ │ └─────────────────────────────────────────────────────────────────────┘ │ │
│ │                                                                         │ │ │
│ │ ┌─────────────────────────────────────────────────────────────────────┐ │ │
│ │ │ Anthropic配置                                                       │ │ │
│ │ │ [类似OpenAI的配置界面布局]                                          │ │ │
│ │ └─────────────────────────────────────────────────────────────────────┘ │ │
│ │                                                                         │ │ │
│ │ ┌─────────────────────────────────────────────────────────────────────┐ │ │
│ │ │ 其他AI服务配置 (Google, ModelScope, SiliconFlow, Ollama等)         │ │ │
│ │ │ [展开/折叠显示各服务商配置]                                         │ │ │
│ │ └─────────────────────────────────────────────────────────────────────┘ │ │
│ │                                                                         │ │ │
│ │ 自定义模型管理                                                         │ │ │
│ │ ┌─────────────────────────────────────────────────────────────────────┐ │ │
│ │ │ 已配置模型列表                                                      │ │ │
│ │ │ [表格显示: 模型名称 | 服务商 | 状态 | 操作]                         │ │ │
│ │ │ [添加模型] [编辑模型] [删除模型] [批量测试] [导入配置] [导出配置]   │ │ │
│ │ └─────────────────────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 界面设置区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 主题设置                                                                │ │
│ │ 主题模式: ○ 明亮主题  ○ 暗黑主题  ● 跟随系统                          │ │
│ │ 主题色彩: [色彩选择器] [预设主题] [自定义主题]                          │ │
│ │                                                                         │ │ │
│ │ 字体设置                                                                │ │
│ │ 界面字体: [字体选择框] 字体大小: [滑动条] 行间距: [滑动条]             │ │ │
│ │ 编辑器字体: [等宽字体选择] 字体大小: [滑动条]                          │ │ │
│ │                                                                         │ │ │
│ │ 布局设置                                                                │ │ │
│ │ 侧边栏宽度: [滑动条] 工具栏显示: [复选框组] 状态栏显示: [复选框组]     │ │ │
│ │ 窗口设置: ☑ 记住窗口位置 ☑ 记住窗口大小 ☑ 启动时最大化               │ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 功能设置区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 自动保存设置                                                            │ │ │
│ │ ☑ 启用自动保存 间隔时间: [数字输入] 分钟                               │ │ │
│ │ ☑ 启用版本历史 保留版本数: [数字输入] 个                               │ │ │
│ │                                                                         │ │ │
│ │ AI功能设置                                                              │ │ │
│ │ ☑ 启用降AI味功能 ☑ 启用智能上下文 ☑ 启用内容检查                      │ │ │
│ │ ☑ 启用创作提醒 ☑ 启用进度跟踪 ☑ 启用统计分析                          │ │ │
│ │                                                                         │ │ │
│ │ 性能设置                                                                │ │ │
│ │ 最大上下文长度: [数字输入] tokens                                      │ │ │
│ │ API请求超时: [数字输入] 秒 并发请求数: [数字输入]                      │ │ │
│ │ 缓存大小限制: [数字输入] MB 自动清理: [复选框]                         │ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 数据管理区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 数据存储信息                                                            │ │ │
│ │ 数据目录: [路径显示] [更改目录] [打开目录]                              │ │ │
│ │ 数据库大小: [大小显示] 缓存大小: [大小显示] 总占用: [大小显示]          │ │ │
│ │                                                                         │ │ │
│ │ 备份恢复                                                                │ │ │
│ │ [创建备份] [恢复备份] [自动备份设置] [备份历史管理]                     │ │ │
│ │                                                                         │ │ │
│ │ 数据清理                                                                │ │ │
│ │ [清除缓存] [清除日志] [清除临时文件] [重置所有数据]                     │ │ │
│ │                                                                         │ │ │
│ │ 导入导出                                                                │ │ │
│ │ [导出配置] [导入配置] [导出项目] [导入项目]                             │ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 操作控制区                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ [保存所有设置] [取消修改] [恢复默认] [应用设置] [重启应用]              │ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

---

## 8. Glassmorphism UI设计规范

### 8.1 设计理念

Glassmorphism（玻璃拟态）是一种现代化的UI设计风格，通过半透明效果、模糊背景和精致的边框创造出类似玻璃的视觉效果。

#### 8.1.1 核心设计原则
- **透明度层次**: 使用不同透明度创建视觉层次
- **模糊效果**: 背景模糊增强深度感
- **精致边框**: 细腻的边框增加精致感
- **色彩和谐**: 柔和的色彩搭配
- **光影效果**: 微妙的阴影和高光

### 8.2 色彩系统设计

#### 8.2.1 色彩使用限制和规范

**严格禁止的颜色**
- **禁止使用紫色系**：包括但不限于紫色(#800080)、紫罗兰(#8A2BE2)、薰衣草(#E6E6FA)等所有紫色调
- **原因说明**：紫色系与应用的清新、现代化定位不符，容易造成视觉疲劳

**推荐色彩方向**
- **主色调**：清新的蓝绿色系、现代化的青色系
- **辅助色**：温暖的橙色系、活力的绿色系
- **中性色**：优雅的灰色系、纯净的白色系

#### 8.2.2 可视化颜色表系统

**颜色表功能设计**
```typescript
interface ColorPalette {
  // 颜色分类
  categories: {
    primary: ColorSet;
    secondary: ColorSet;
    accent: ColorSet;
    neutral: ColorSet;
    semantic: ColorSet; // 语义化颜色（成功、警告、错误等）
  };

  // 颜色管理功能
  functions: {
    preview: (colorCode: string) => void;
    apply: (colorCode: string, target: string) => void;
    save: (palette: ColorPalette) => void;
    export: () => string;
    import: (data: string) => void;
  };
}

interface ColorSet {
  name: string;
  colors: {
    50: string;   // 最浅
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;  // 基准色
    600: string;
    700: string;
    800: string;
    900: string;  // 最深
  };
  forbidden?: string[]; // 禁用的颜色
}
```

**可视化颜色表界面**
```
┌─────────────────────────────────────────────────────────────┐
│                    可视化颜色表                              │
├─────────────────────────────────────────────────────────────┤
│ 颜色分类选择                                               │
│ [主色调] [辅助色] [强调色] [中性色] [语义色] [自定义]       │
│                                                             │
│ 主色调 - 清新蓝绿色系                                      │
│ ┌─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┐ │
│ │ 50  │ 100 │ 200 │ 300 │ 400 │ 500 │ 600 │ 700 │ 800 │ 900 │ │
│ │█████│█████│█████│█████│█████│█████│█████│█████│█████│█████│ │
│ │#f0fd│#ccfb│#99f6│#5eea│#2dd4│#14b8│#0d94│#0f76│#115e│#134e│ │
│ │fa   │f1   │e4   │d4   │bf   │a6   │88   │6e   │59   │4a   │ │
│ └─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┘ │
│                                                             │
│ 禁用颜色提醒                                               │
│ ⚠️ 紫色系已被禁用 [查看详情]                                │
│                                                             │
│ 颜色操作                                                   │
│ [预览效果] [应用到界面] [保存配色] [导出配置] [重置默认]     │
└─────────────────────────────────────────────────────────────┘
```

#### 8.2.3 气泡通知信息系统

**通知系统设计**
```typescript
interface NotificationSystem {
  // 通知类型
  types: {
    success: NotificationType;
    warning: NotificationType;
    error: NotificationType;
    info: NotificationType;
  };

  // 通知方法
  show: (type: string, message: string, options?: NotificationOptions) => void;
  hide: (id: string) => void;
  clear: () => void;
}

interface NotificationType {
  icon: string;
  color: string;
  backgroundColor: string;
  borderColor: string;
  duration: number;
}

interface NotificationOptions {
  duration?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  closable?: boolean;
  action?: {
    text: string;
    handler: () => void;
  };
}
```

**气泡通知界面设计**
```
┌─────────────────────────────────────────┐
│ 🎉 操作成功                              │
│ 大纲生成完成，共生成15个章节             │
│                              [关闭] [×] │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│ ⚠️ 注意                                 │
│ API调用频率过高，请稍后再试              │
│                              [重试] [×] │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│ ❌ 错误                                 │
│ 网络连接失败，请检查网络设置             │
│                              [设置] [×] │
└─────────────────────────────────────────┘
```

#### 8.2.4 明亮主题色彩
```css
/* 主色调 */
--primary-color: #3B82F6;      /* 蓝色 */
--secondary-color: #10B981;    /* 绿色 */
--accent-color: #F59E0B;       /* 橙色 */
--danger-color: #EF4444;       /* 红色 */

/* 背景色 */
--bg-primary: rgba(255, 255, 255, 0.1);
--bg-secondary: rgba(255, 255, 255, 0.05);
--bg-tertiary: rgba(255, 255, 255, 0.02);

/* 文字色 */
--text-primary: #1F2937;
--text-secondary: #6B7280;
--text-tertiary: #9CA3AF;

/* 边框色 */
--border-primary: rgba(255, 255, 255, 0.2);
--border-secondary: rgba(255, 255, 255, 0.1);
```

#### 8.2.5 暗黑主题色彩
```css
/* 主色调 */
--primary-color: #60A5FA;      /* 亮蓝色 */
--secondary-color: #34D399;    /* 亮绿色 */
--accent-color: #FBBF24;       /* 亮橙色 */
--danger-color: #F87171;       /* 亮红色 */

/* 背景色 */
--bg-primary: rgba(0, 0, 0, 0.3);
--bg-secondary: rgba(0, 0, 0, 0.2);
--bg-tertiary: rgba(0, 0, 0, 0.1);

/* 文字色 */
--text-primary: #F9FAFB;
--text-secondary: #D1D5DB;
--text-tertiary: #9CA3AF;

/* 边框色 */
--border-primary: rgba(255, 255, 255, 0.1);
--border-secondary: rgba(255, 255, 255, 0.05);
```

### 8.3 组件设计规范

#### 8.3.1 全局统一组件系统

**组件设计原则**
- 所有组件必须遵循Glassmorphism设计风格
- 统一的交互反馈和动画效果
- 支持明亮和暗黑两套主题
- 禁止使用emoji表情包，统一使用SVG矢量图标
- 组件状态要有明确的视觉反馈

**SVG图标系统**
```javascript
const iconSystem = {
  // 功能图标
  functional: {
    home: '<svg>...</svg>',           // 首页
    outline: '<svg>...</svg>',        // 大纲
    chapter: '<svg>...</svg>',        // 章节
    character: '<svg>...</svg>',      // 人物
    analysis: '<svg>...</svg>',       // 分析
    statistics: '<svg>...</svg>',     // 统计
    chat: '<svg>...</svg>',          // 聊天
    settings: '<svg>...</svg>',       // 设置
    save: '<svg>...</svg>',          // 保存
    export: '<svg>...</svg>',        // 导出
    import: '<svg>...</svg>',        // 导入
    edit: '<svg>...</svg>',          // 编辑
    delete: '<svg>...</svg>',        // 删除
    add: '<svg>...</svg>',           // 添加
    search: '<svg>...</svg>',        // 搜索
    filter: '<svg>...</svg>',        // 筛选
    refresh: '<svg>...</svg>',       // 刷新
    close: '<svg>...</svg>',         // 关闭
    minimize: '<svg>...</svg>',      // 最小化
    maximize: '<svg>...</svg>'       // 最大化
  },

  // 状态图标
  status: {
    success: '<svg>...</svg>',       // 成功
    error: '<svg>...</svg>',         // 错误
    warning: '<svg>...</svg>',       // 警告
    info: '<svg>...</svg>',          // 信息
    loading: '<svg>...</svg>',       // 加载中
    connected: '<svg>...</svg>',     // 已连接
    disconnected: '<svg>...</svg>',  // 未连接
    completed: '<svg>...</svg>',     // 已完成
    inProgress: '<svg>...</svg>',    // 进行中
    pending: '<svg>...</svg>'        // 待处理
  },

  // AI相关图标
  ai: {
    robot: '<svg>...</svg>',         // AI机器人
    brain: '<svg>...</svg>',         // AI大脑
    magic: '<svg>...</svg>',         // AI魔法
    generate: '<svg>...</svg>',      // 生成
    optimize: '<svg>...</svg>',      // 优化
    analyze: '<svg>...</svg>',       // 分析
    template: '<svg>...</svg>',      // 模板
    prompt: '<svg>...</svg>',        // 提示词
    context: '<svg>...</svg>',       // 上下文
    vector: '<svg>...</svg>'         // 向量
  }
};
```

**组件状态颜色系统**
```javascript
const componentColors = {
  // 主要状态色
  primary: {
    default: '#3B82F6',    // 蓝色 - 主要操作
    hover: '#2563EB',      // 深蓝 - 悬停状态
    active: '#1D4ED8',     // 更深蓝 - 激活状态
    disabled: '#9CA3AF'    // 灰色 - 禁用状态
  },

  // 成功状态色
  success: {
    default: '#10B981',    // 绿色 - 成功
    hover: '#059669',      // 深绿 - 悬停
    active: '#047857',     // 更深绿 - 激活
    light: '#D1FAE5'       // 浅绿 - 背景
  },

  // 警告状态色
  warning: {
    default: '#F59E0B',    // 橙色 - 警告
    hover: '#D97706',      // 深橙 - 悬停
    active: '#B45309',     // 更深橙 - 激活
    light: '#FEF3C7'       // 浅橙 - 背景
  },

  // 错误状态色
  error: {
    default: '#EF4444',    // 红色 - 错误
    hover: '#DC2626',      // 深红 - 悬停
    active: '#B91C1C',     // 更深红 - 激活
    light: '#FEE2E2'       // 浅红 - 背景
  },

  // 信息状态色
  info: {
    default: '#6366F1',    // 紫色 - 信息
    hover: '#5B21B6',      // 深紫 - 悬停
    active: '#4C1D95',     // 更深紫 - 激活
    light: '#EDE9FE'       // 浅紫 - 背景
  }
};
```

#### 8.3.2 按钮组件
```css
/* 主要按钮 */
.btn-primary {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.3) 0%,
    rgba(59, 130, 246, 0.1) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  color: var(--primary-color);
  padding: 12px 24px;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.4) 0%,
    rgba(59, 130, 246, 0.2) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

/* 次要按钮 */
.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: var(--text-primary);
  padding: 12px 24px;
}
```

#### 7.3.3 卡片组件
```css
.glass-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 24px;
  transition: all 0.3s ease;
}

.glass-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
}
```

#### 7.3.4 输入框组件
```css
.glass-input {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--text-primary);
  padding: 12px 16px;
  transition: all 0.3s ease;
}

.glass-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}
```

#### 8.3.5 气泡通知组件
```css
/* 通知容器 */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
}

/* 通知气泡 */
.notification-bubble {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 16px 20px;
  margin-bottom: 12px;
  min-width: 300px;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-bubble.show {
  transform: translateX(0);
  opacity: 1;
}

/* 成功通知 */
.notification-bubble.success {
  border-left: 4px solid var(--success-color);
}

.notification-bubble.success .notification-icon {
  color: var(--success-color);
}

/* 错误通知 */
.notification-bubble.error {
  border-left: 4px solid var(--error-color);
}

.notification-bubble.error .notification-icon {
  color: var(--error-color);
}

/* 警告通知 */
.notification-bubble.warning {
  border-left: 4px solid var(--warning-color);
}

.notification-bubble.warning .notification-icon {
  color: var(--warning-color);
}

/* 信息通知 */
.notification-bubble.info {
  border-left: 4px solid var(--info-color);
}

.notification-bubble.info .notification-icon {
  color: var(--info-color);
}

/* 通知内容布局 */
.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.notification-icon {
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}

.notification-text {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.notification-message {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}
```

**通知系统JavaScript实现**
```javascript
class NotificationSystem {
  constructor() {
    this.container = this.createContainer();
    this.notifications = new Map();
  }

  createContainer() {
    const container = document.createElement('div');
    container.className = 'notification-container';
    document.body.appendChild(container);
    return container;
  }

  show(options) {
    const {
      type = 'info',
      title,
      message,
      duration = 5000,
      persistent = false
    } = options;

    const id = Date.now().toString();
    const notification = this.createNotification(id, type, title, message);

    this.container.appendChild(notification);
    this.notifications.set(id, notification);

    // 显示动画
    requestAnimationFrame(() => {
      notification.classList.add('show');
    });

    // 自动隐藏
    if (!persistent && duration > 0) {
      setTimeout(() => {
        this.hide(id);
      }, duration);
    }

    return id;
  }

  createNotification(id, type, title, message) {
    const notification = document.createElement('div');
    notification.className = `notification-bubble ${type}`;
    notification.dataset.id = id;

    const iconMap = {
      success: '✓',
      error: '✕',
      warning: '⚠',
      info: 'ℹ'
    };

    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon">${iconMap[type]}</div>
        <div class="notification-text">
          ${title ? `<div class="notification-title">${title}</div>` : ''}
          <div class="notification-message">${message}</div>
        </div>
        <button class="notification-close" onclick="notificationSystem.hide('${id}')">×</button>
      </div>
    `;

    return notification;
  }

  hide(id) {
    const notification = this.notifications.get(id);
    if (notification) {
      notification.classList.remove('show');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
        this.notifications.delete(id);
      }, 400);
    }
  }

  // 便捷方法
  success(title, message, options = {}) {
    return this.show({ ...options, type: 'success', title, message });
  }

  error(title, message, options = {}) {
    return this.show({ ...options, type: 'error', title, message });
  }

  warning(title, message, options = {}) {
    return this.show({ ...options, type: 'warning', title, message });
  }

  info(title, message, options = {}) {
    return this.show({ ...options, type: 'info', title, message });
  }
}

// 全局通知系统实例
const notificationSystem = new NotificationSystem();
```

### 8.4 动画效果规范

#### 8.4.1 页面切换动画
```css
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s ease;
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
```

#### 8.4.2 加载动画
```css
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(59, 130, 246, 0.1);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

---

## 9. 详细开发路线

### 9.1 开发路线总览

本开发路线按功能模块组织，确保每个功能都有明确的开发步骤和依赖关系。开发顺序基于功能间的依赖关系和重要性优先级。

### 9.2 基础架构开发路线

#### 9.2.1 项目初始化
**开发内容**：
- 创建Electron + Vue 3项目结构
- 配置TypeScript、Vite、Tailwind CSS
- 设置ESLint、Prettier代码规范
- 配置Git版本控制
- 设置项目构建脚本

**技术要点**：
- 确保零配置启动的内置依赖策略
- 配置跨平台兼容性
- 建立代码质量检查机制

#### 9.2.2 数据库架构
**开发内容**：
- 设计SQLite数据库表结构
- 实现Prisma ORM配置
- 创建数据库迁移脚本
- 编写基础数据访问层
- 实现数据库版本管理

**核心表结构**：
- 项目表（projects）
- 大纲表（outlines）
- 章节表（chapters）
- 角色表（characters）
- 角色关系表（character_relationships）
- 提示词模板表（prompt_templates）
- API配置表（api_configs）
- 用户设置表（user_settings）

#### 9.2.3 基础UI框架
**开发内容**：
- 实现Glassmorphism设计系统
- 创建基础组件库
- 实现明暗主题切换
- 搭建主界面布局框架
- 实现可视化颜色表系统
- 创建气泡通知系统

**组件清单**：
- 按钮组件（多状态、多颜色）
- 输入框组件（文本、数字、下拉）
- 卡片组件（玻璃拟态效果）
- 模态框组件
- 表格组件
- 树形组件
- 图表组件
- SVG图标系统

#### 9.2.4 AI服务集成架构
**开发内容**：
- 设计统一的AI服务接口
- 实现多厂商API适配器
- 创建智能API地址检测机制
- 实现统一API保存管理系统
- 建立错误处理和重试机制
- 实现API连接测试功能

**支持的AI服务**：
- OpenAI (GPT系列)
- Anthropic (Claude系列)
- Google (Gemini系列)
- ModelScope
- Ollama本地模型
- SiliconFlow
- 自定义OpenAI兼容API

### 9.3 核心功能开发路线

#### 9.3.1 大纲生成模块
**开发内容**：
- 实现大纲生成界面（左右40:60布局）
- 集成AI大纲生成功能
- 实现提示词模板系统
- 添加生成结果管理
- 实现生成范围控制
- 添加小说类型、主题、风格配置
- 实现人物数量配置

**界面组件**：
- 模型选择下拉框
- 提示词模板管理
- 基本信息配置表单
- 生成范围设置
- 大纲结果展示区
- 操作按钮组

#### 9.3.2 大纲编辑模块
**开发内容**：
- 实现大纲编辑界面（左右40:60布局）
- 添加AI辅助编辑功能
- 实现版本历史管理
- 添加内容保存功能
- 实现小说标题AI生成
- 实现中心思想AI生成
- 实现故事梗概AI生成
- 实现世界观设定AI生成

**编辑功能**：
- 富文本编辑器
- AI辅助优化
- 内容版本对比
- 自动保存机制

#### 9.3.3 章节编辑模块
**开发内容**：
- 实现章节编辑界面（左右40:60布局）
- 添加章节列表管理
- 实现章节排序和操作
- 集成AI章节生成
- 实现章节标题AI生成
- 实现章节摘要生成
- 添加角色选择功能

**管理功能**：
- 章节增删改查
- 章节拖拽排序
- 批量操作
- 章节状态管理

#### 9.3.4 章节生成模块
**开发内容**：
- 实现富文本编辑器
- 集成AI内容生成
- 实现上下文管理
- 添加降AI味功能
- 实现内容润色功能
- 添加目标字数控制
- 实现选定文本润色

**生成功能**：
- 智能上下文提取
- 多模型内容生成
- 实时字数统计
- 内容质量优化

#### 9.3.5 人物编辑模块
**开发内容**：
- 实现人物编辑界面（左右40:60布局）
- 添加角色列表管理
- 实现角色增删改查
- 集成AI角色生成
- 实现角色详情编辑
- 添加角色搜索筛选

**角色管理**：
- 角色基本信息
- 外貌描述
- 性格特点
- 背景故事
- 能力设定

#### 9.3.6 人物关系图模块
**开发内容**：
- 实现关系图可视化界面（左右40:60布局）
- 添加关系图绘制功能
- 实现关系添加/编辑/删除
- 添加关系类型管理
- 实现关系图导出
- 添加图形布局算法

**可视化功能**：
- 节点拖拽
- 关系连线
- 图形缩放
- 布局优化

### 9.4 辅助功能开发路线

#### 9.4.1 章节分析模块
**开发内容**：
- 实现章节分析界面（左右40:60布局）
- 添加多维度分析功能
- 实现分析结果展示
- 添加章节改进功能
- 实现分析报告生成

**分析维度**：
- 核心剧情分析
- 故事梗概提取
- 优缺点分析
- 角色标注
- 物品标注
- 改进建议

#### 9.4.2 统计信息模块
**开发内容**：
- 实现统计信息界面（左右40:60布局）
- 添加实时数据统计
- 实现数据可视化
- 添加统计报告导出
- 实现进度跟踪

**统计内容**：
- 概览统计
- 章节统计
- 创作趋势
- 完成度分析

#### 9.4.3 AI聊天模块
**开发内容**：
- 实现AI聊天界面（左右40:60布局）
- 添加多模型对话
- 实现对话历史管理
- 添加快速提问模板
- 实现会话保存功能

**聊天功能**：
- 实时对话
- 模型切换
- 历史记录
- 写作咨询

#### 9.4.4 提示词库模块
**开发内容**：
- 实现提示词库界面（左右40:60布局）
- 添加内置提示词模板
- 实现自定义模板管理
- 添加模板分类功能
- 实现模板导入导出
- 严格遵循中英文标点符号规范

**内置模板类型**：
- 大纲相关
- 细纲
- 章节
- 世界观
- 剧情线
- 续写
- 扩写
- 润色
- 改写
- 优化建议
- 金手指生成
- 黄金开篇
- 写作风格
- 写作要求
- 人设生成
- 审稿
- 仿写
- 短篇

#### 9.4.5 上下文管理模块
**开发内容**：
- 实现上下文管理界面（左右40:60布局）
- 添加智能上下文提取
- 实现角色状态跟踪
- 添加情节线管理
- 实现伏笔管理
- 添加世界观一致性检查

**管理功能**：
- 上下文配置
- 自动提取
- 手动编辑
- 状态跟踪

#### 9.4.6 向量库检索模块
**开发内容**：
- 实现向量库检索界面（左右40:60布局）
- 添加嵌入模型配置选项
- 实现语义检索功能
- 添加检索结果管理
- 实现索引构建和维护

**检索功能**：
- 语义搜索
- 相似度排序
- 结果筛选
- 历史记录

#### 9.4.7 降AI味功能模块
**开发内容**：
- 实现降AI味处理界面
- 添加AI味检测算法
- 实现表达优化功能
- 添加风格调整选项
- 实现批量处理功能

**优化功能**：
- 机械化表达检测
- 情感表达增强
- 个性化处理
- 风格一致性

#### 9.4.8 记忆窗口功能模块
**开发内容**：
- 实现窗口状态记忆
- 添加布局记忆功能
- 实现主题记忆
- 添加工作区记忆
- 实现多显示器支持

**记忆内容**：
- 窗口大小位置
- 侧边栏宽度
- 面板分割比例
- 最后打开项目

#### 9.4.9 运行日志模块
**开发内容**：
- 实现日志记录系统
- 添加纯中文显示功能
- 实现日志分类管理
- 添加日志查看界面
- 实现日志清理功能

**日志类型**：
- 操作日志
- 错误日志
- 性能日志
- AI调用日志

### 9.5 系统功能开发路线

#### 9.5.1 设置模块
**开发内容**：
- 实现设置界面（全屏显示）
- 添加API配置管理
- 实现智能API检测
- 添加统一API管理
- 实现配置导入导出
- 添加主题设置
- 实现功能开关配置

**设置分类**：
- AI配置
- 界面设置
- 功能设置
- 数据管理
- 高级设置

#### 9.5.2 网络小说平台适配功能
**开发内容**：
- 实现平台适配系统
- 添加平台配置管理
- 实现内容自动调整
- 添加平台特色元素
- 实现适配预览功能

**支持平台**：
- 起点中文网
- 番茄小说
- 晋江文学城
- 17K小说网
- 纵横中文网
- 七猫免费小说
- 飞卢小说网

### 9.6 界面设计开发路线

#### 9.6.1 Glassmorphism设计系统实现
**开发内容**：
- 实现玻璃拟态视觉效果
- 创建明暗两套主题
- 严格禁止紫色系颜色使用
- 实现可视化颜色表
- 添加气泡通知系统
- 禁止使用emoji表情包
- 统一使用SVG矢量图标

**设计规范**：
- 左右两区域布局（40:60比例）
- 统一的组件风格
- 一致的交互体验
- 无障碍访问支持

#### 9.6.2 全部功能界面布局实现
**开发内容**：
按顺序实现所有功能模块的界面布局，确保无断层：

1. **大纲生成界面**（左右40:60布局）
2. **大纲编辑界面**（左右40:60布局）
3. **章节编辑界面**（左右40:60布局）
4. **章节生成界面**（左右40:60布局）
5. **人物编辑界面**（左右40:60布局）
6. **人物关系图界面**（左右40:60布局）
7. **章节分析界面**（左右40:60布局）
8. **统计信息界面**（左右40:60布局）
9. **AI聊天界面**（左右40:60布局）
10. **提示词库界面**（左右40:60布局）
11. **上下文管理界面**（左右40:60布局）
12. **向量库检索界面**（左右40:60布局）
13. **降AI味处理界面**（左右40:60布局）
14. **记忆窗口设置界面**（左右40:60布局）
15. **运行日志界面**（左右40:60布局）
16. **网络小说平台适配界面**（左右40:60布局）
17. **设置界面**（全屏显示）

**界面要求**：
- 所有界面使用真实功能描述，禁止假数据
- ASCII绘图详细展示布局结构
- 统一的视觉风格和交互模式

### 9.7 集成测试开发路线

#### 9.7.1 功能集成测试
**测试内容**：
- 各功能模块间的数据流转
- AI服务调用的稳定性
- 界面响应性能测试
- 数据持久化测试
- 错误处理机制测试

#### 9.7.2 用户体验测试
**测试内容**：
- 界面易用性测试
- 功能完整性验证
- 性能优化测试
- 兼容性测试
- 无障碍访问测试

### 9.8 打包部署开发路线

#### 9.8.1 多平台打包配置
**开发内容**：
- 配置Electron Builder
- 设置Inno Setup安装程序
- 配置PyInstaller打包
- 设置MSI安装包生成
- 实现跨平台构建脚本

**打包工具配置**：
1. **Electron Builder配置**
   - Windows NSIS安装程序
   - macOS DMG和PKG
   - Linux AppImage、deb、rpm

2. **Inno Setup配置**
   - Windows专用安装程序
   - 自定义安装界面
   - 卸载程序配置

3. **PyInstaller配置**
   - 单文件打包
   - 依赖库处理
   - 资源文件打包

4. **MSI配置**
   - WiX Toolset配置
   - Windows Installer包
   - 企业级部署支持

#### 9.8.2 发布流程配置
**开发内容**：
- 自动化构建脚本
- 版本号管理
- 更新检查机制
- 发布包验证
- 分发渠道配置

### 9.9 开发路线依赖关系

#### 9.9.1 核心依赖链
```
基础架构 → AI服务集成 → 核心功能模块 → 辅助功能模块 → 界面优化 → 集成测试 → 打包部署
```

#### 9.9.2 功能模块依赖
- **设置模块** → 所有AI相关功能（API配置基础）
- **大纲生成** → 大纲编辑 → 章节编辑 → 章节生成
- **人物编辑** → 人物关系图
- **提示词模板系统** → 所有AI生成功能
- **上下文管理** → 章节生成、章节分析
- **向量库检索** → 上下文管理、内容搜索
- **降AI味功能** → 章节生成、内容优化
- **统计信息** → 所有内容创建功能
- **网络小说平台适配** → 章节生成、内容优化
- **记忆窗口功能** → 界面状态管理
- **运行日志功能** → 系统监控和调试

#### 9.9.3 技术依赖
- **数据库架构** → 所有数据存储功能
- **AI服务集成** → 所有AI相关功能
- **基础UI框架** → 所有界面功能
- **设置模块** → 所有配置相关功能

### 9.10 开发路线验收标准

#### 9.10.1 功能完整性标准
- 所有17个核心功能模块完整实现
- 每个功能都有对应的界面和后端逻辑
- 所有AI服务集成正常工作
- 数据持久化功能正常

#### 9.10.2 质量标准
- 代码覆盖率达到80%以上
- 界面响应时间小于200ms
- AI调用成功率95%以上
- 内存使用稳定无泄漏

#### 9.10.3 用户体验标准
- 界面操作直观易懂
- 功能流程顺畅无阻
- 错误提示清晰准确
- 帮助文档完整详细

### 9.11 技术风险评估

#### 9.11.1 高风险项
- **AI API稳定性**: 多厂商API的稳定性和兼容性
- **大文件性能**: 长篇小说的性能处理
- **跨平台兼容**: 不同操作系统的兼容性问题

#### 9.11.2 风险缓解策略
- **API备用方案**: 实现多模型自动切换
- **性能监控**: 建立性能监控和优化机制
- **充分测试**: 在多平台进行充分测试

### 9.12 质量保证计划

#### 9.12.1 代码质量
- 使用TypeScript确保类型安全
- 配置ESLint和Prettier保证代码规范
- 实施代码审查机制
- 维护单元测试覆盖率>80%

#### 9.12.2 用户体验
- 定期进行用户体验测试
- 收集用户反馈并及时改进
- 保持界面响应时间<200ms
- 确保应用稳定性>99%

---

## 10. 部署与发布

### 10.1 打包配置

#### 10.1.1 Electron Builder配置
```json
{
  "build": {
    "appId": "com.ainovel.assistant",
    "productName": "AI小说助手",
    "directories": {
      "output": "dist"
    },
    "files": [
      "dist-electron/**/*",
      "dist/**/*",
      "node_modules/**/*"
    ],
    "win": {
      "target": "nsis",
      "icon": "resources/icon.ico"
    },
    "mac": {
      "target": "dmg",
      "icon": "resources/icon.icns"
    },
    "linux": {
      "target": "AppImage",
      "icon": "resources/icon.png"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true
    }
  }
}
```

#### 10.1.2 自动更新配置
```typescript
// 自动更新服务
import { autoUpdater } from 'electron-updater';

export class UpdateService {
  constructor() {
    autoUpdater.checkForUpdatesAndNotify();
    autoUpdater.on('update-available', this.onUpdateAvailable);
    autoUpdater.on('update-downloaded', this.onUpdateDownloaded);
  }

  private onUpdateAvailable = () => {
    // 通知用户有新版本可用
  };

  private onUpdateDownloaded = () => {
    // 提示用户重启应用更新
  };
}
```

### 10.2 多平台打包工具配置

#### 10.2.1 PyInstaller配置（如需Python后端）
```python
# build.spec
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('config', 'config')
    ],
    hiddenimports=[
        'sqlite3',
        'cryptography',
        'requests',
        'fastapi',
        'uvicorn'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ai-novel-backend',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico'
)
```

#### 10.2.2 Inno Setup配置
```pascal
; AI小说助手安装脚本
[Setup]
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName=AI小说助手
AppVersion=1.0.0
AppPublisher=AI小说助手开发团队
AppPublisherURL=https://ainovel.com
AppSupportURL=https://ainovel.com/support
AppUpdatesURL=https://ainovel.com/updates
DefaultDirName={autopf}\AI小说助手
DefaultGroupName=AI小说助手
AllowNoIcons=yes
LicenseFile=LICENSE.txt
InfoBeforeFile=README.txt
OutputDir=dist
OutputBaseFilename=AI小说助手-Setup-v1.0.0
SetupIconFile=resources\icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=lowest
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
Source: "dist\AI小说助手.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\AI小说助手"; Filename: "{app}\AI小说助手.exe"
Name: "{group}\{cm:UninstallProgram,AI小说助手}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\AI小说助手"; Filename: "{app}\AI小说助手.exe"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\AI小说助手"; Filename: "{app}\AI小说助手.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\AI小说助手.exe"; Description: "{cm:LaunchProgram,AI小说助手}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{userappdata}\AI小说助手"
```

#### 10.2.3 MSI打包配置

**使用WiX Toolset创建MSI安装包**

**Product.wxs配置文件**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*"
           Name="AI小说助手"
           Language="2052"
           Version="*******"
           Manufacturer="AI小说助手开发团队"
           UpgradeCode="12345678-1234-1234-1234-123456789012">

    <Package InstallerVersion="200"
             Compressed="yes"
             InstallScope="perMachine"
             Description="AI小说助手安装包"
             Comments="基于AI的智能小说创作工具" />

    <MajorUpgrade DowngradeErrorMessage="已安装更新版本的AI小说助手。" />
    <MediaTemplate EmbedCab="yes" />

    <!-- 安装目录结构 -->
    <Feature Id="ProductFeature" Title="AI小说助手" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentRef Id="ApplicationShortcut" />
      <ComponentRef Id="DesktopShortcut" />
    </Feature>

    <!-- 目录结构定义 -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="AI小说助手" />
      </Directory>

      <!-- 开始菜单 -->
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="AI小说助手"/>
      </Directory>

      <!-- 桌面 -->
      <Directory Id="DesktopFolder" Name="Desktop" />
    </Directory>

    <!-- 快捷方式组件 -->
    <DirectoryRef Id="ApplicationProgramsFolder">
      <Component Id="ApplicationShortcut" Guid="************************************">
        <Shortcut Id="ApplicationStartMenuShortcut"
                  Name="AI小说助手"
                  Description="AI驱动的智能小说创作工具"
                  Target="[#AI小说助手.exe]"
                  WorkingDirectory="INSTALLFOLDER"/>
        <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
        <RegistryValue Root="HKCU"
                       Key="Software\AI小说助手\Installed"
                       Name="installed"
                       Type="integer"
                       Value="1"
                       KeyPath="yes"/>
      </Component>
    </DirectoryRef>

    <!-- 桌面快捷方式 -->
    <DirectoryRef Id="DesktopFolder">
      <Component Id="DesktopShortcut" Guid="************************************">
        <Shortcut Id="DesktopShortcut"
                  Name="AI小说助手"
                  Description="AI驱动的智能小说创作工具"
                  Target="[#AI小说助手.exe]"
                  WorkingDirectory="INSTALLFOLDER"/>
        <RegistryValue Root="HKCU"
                       Key="Software\AI小说助手\Desktop"
                       Name="installed"
                       Type="integer"
                       Value="1"
                       KeyPath="yes"/>
      </Component>
    </DirectoryRef>
  </Product>

  <!-- 文件组件组 -->
  <Fragment>
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component Id="AI小说助手.exe" Guid="12345678-1234-1234-1234-123456789015">
        <File Id="AI小说助手.exe"
              Source="dist\AI小说助手.exe"
              KeyPath="yes"
              Checksum="yes"/>
      </Component>

      <!-- 其他必要文件 -->
      <Component Id="Resources" Guid="12345678-1234-1234-1234-123456789016">
        <File Id="resources.pak" Source="dist\resources.pak" />
        <File Id="icudtl.dat" Source="dist\icudtl.dat" />
      </Component>

      <!-- 本地化文件 -->
      <Component Id="Locales" Guid="12345678-1234-1234-1234-123456789017">
        <File Id="zh_CN.pak" Source="dist\locales\zh-CN.pak" />
        <File Id="en_US.pak" Source="dist\locales\en-US.pak" />
      </Component>
    </ComponentGroup>
  </Fragment>
</Wix>
```

**构建MSI的批处理脚本**
```batch
@echo off
echo 开始构建MSI安装包...

REM 清理旧的构建文件
if exist "*.wixobj" del "*.wixobj"
if exist "*.msi" del "*.msi"

REM 编译WiX源文件
echo 编译WiX源文件...
candle.exe Product.wxs -out Product.wixobj
if errorlevel 1 (
    echo 编译失败！
    pause
    exit /b 1
)

REM 链接生成MSI
echo 生成MSI安装包...
light.exe Product.wixobj -out "AI小说助手-v1.0.0.msi" -ext WixUIExtension
if errorlevel 1 (
    echo 链接失败！
    pause
    exit /b 1
)

echo MSI安装包构建完成！
echo 输出文件: AI小说助手-v1.0.0.msi
pause
```

#### 10.2.4 跨平台打包脚本

**统一构建脚本 (build-all.js)**
```javascript
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class MultiPlatformBuilder {
  constructor() {
    this.platforms = ['win32', 'darwin', 'linux'];
    this.architectures = ['x64', 'arm64'];
  }

  async buildAll() {
    console.log('🚀 开始多平台构建...');

    // 1. 清理构建目录
    this.cleanBuildDir();

    // 2. 构建Electron应用
    await this.buildElectronApp();

    // 3. 为每个平台创建安装包
    for (const platform of this.platforms) {
      await this.buildForPlatform(platform);
    }

    console.log('✅ 所有平台构建完成！');
  }

  async buildForPlatform(platform) {
    console.log(`📦 构建 ${platform} 平台...`);

    switch (platform) {
      case 'win32':
        await this.buildWindows();
        break;
      case 'darwin':
        await this.buildMacOS();
        break;
      case 'linux':
        await this.buildLinux();
        break;
    }
  }

  async buildWindows() {
    // Electron Builder
    execSync('npm run build:win', { stdio: 'inherit' });

    // Inno Setup
    if (fs.existsSync('setup.iss')) {
      execSync('iscc setup.iss', { stdio: 'inherit' });
    }

    // MSI (如果配置了WiX)
    if (fs.existsSync('Product.wxs')) {
      execSync('build-msi.bat', { stdio: 'inherit' });
    }
  }

  async buildMacOS() {
    execSync('npm run build:mac', { stdio: 'inherit' });

    // 代码签名（如果有证书）
    if (process.env.CSC_IDENTITY_AUTO_DISCOVERY !== 'false') {
      console.log('🔐 执行代码签名...');
    }
  }

  async buildLinux() {
    execSync('npm run build:linux', { stdio: 'inherit' });

    // 创建多种Linux包格式
    const formats = ['AppImage', 'deb', 'rpm'];
    for (const format of formats) {
      console.log(`📦 创建 ${format} 包...`);
      execSync(`electron-builder --linux ${format.toLowerCase()}`, { stdio: 'inherit' });
    }
  }

  cleanBuildDir() {
    console.log('🧹 清理构建目录...');
    const dirsToClean = ['dist', 'build', 'release'];

    dirsToClean.forEach(dir => {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true });
      }
    });
  }

  async buildElectronApp() {
    console.log('⚡ 构建Electron应用...');
    execSync('npm run build', { stdio: 'inherit' });
  }
}

// 执行构建
if (require.main === module) {
  const builder = new MultiPlatformBuilder();
  builder.buildAll().catch(console.error);
}

module.exports = MultiPlatformBuilder;
```

### 10.3 发布流程

#### 10.3.1 版本发布检查清单
- [ ] 代码审查通过
- [ ] 所有测试用例通过
- [ ] 性能指标达标
- [ ] 跨平台兼容性验证
- [ ] 安全扫描通过
- [ ] 用户文档更新
- [ ] 安装包测试完成
- [ ] 数字签名验证

#### 10.3.2 自动化构建脚本
```bash
#!/bin/bash
# build.sh - 自动化构建脚本

set -e

echo "开始构建AI小说助手..."

# 1. 清理旧的构建文件
echo "清理构建目录..."
rm -rf dist/
rm -rf build/

# 2. 安装依赖
echo "安装依赖..."
npm ci

# 3. 运行测试
echo "运行测试..."
npm run test

# 4. 构建前端
echo "构建前端..."
npm run build

# 5. 构建Electron应用
echo "构建Electron应用..."
npm run electron:build

# 6. 创建安装包
echo "创建安装包..."
case "$OSTYPE" in
  msys*|win*)
    # Windows
    npm run build:win
    iscc setup.iss  # Inno Setup
    ;;
  darwin*)
    # macOS
    npm run build:mac
    ;;
  linux*)
    # Linux
    npm run build:linux
    ;;
esac

echo "构建完成！"
```

#### 10.3.3 发布渠道
- **GitHub Releases**: 主要发布渠道
- **本地分发**: 直接文件分发
- **应用商店**: 考虑上架Microsoft Store等
- **软件下载站**: 华军软件园、太平洋下载等

---

## 11. 维护与支持

### 11.1 版本管理策略

#### 11.1.1 版本号规则
采用语义化版本控制 (Semantic Versioning)：
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正


### 11.2 用户支持

#### 11.2.1 文档体系
- **用户手册**: 详细的功能使用说明
- **快速入门**: 新用户引导教程
- **FAQ**: 常见问题解答
- **API文档**: 开发者接口文档

#### 11.2.2 反馈渠道
- **GitHub Issues**: 问题报告和功能请求
- **邮件支持**: 技术支持邮箱
- **应用内反馈**: 应用内反馈系统
- **本地帮助**: 应用内帮助文档

---

## 结语

本开发文档详细描述了AI小说助手桌面应用的完整设计方案，包括技术架构、功能模块、界面设计、开发计划等各个方面。通过采用现代化的技术栈和用户友好的设计理念，该应用将为网络小说创作者提供强大的AI辅助创作工具。

在实际开发过程中，应当严格按照本文档的规范执行，同时保持灵活性，根据实际情况和用户反馈进行适当调整和优化。

